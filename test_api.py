#!/usr/bin/env python3
"""
Test script for UUID Management System API
Demonstrates all the key functionality
"""

import requests
import json
import time
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_admin_login():
    """Test admin login and return token"""
    print("🔐 Testing admin login...")
    data = {
        "username": "admin",
        "password": "admin123"
    }
    response = requests.post(f"{BASE_URL}/admin/login", data=data)
    print(f"Status: {response.status_code}")
    result = response.json()
    print(f"Token received: {result['access_token'][:50]}...")
    print()
    return result['access_token']

def test_create_uuid(token):
    """Test creating a new UUID"""
    print("➕ Testing UUID creation...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Create UUID with expiration
    future_date = (datetime.now() + timedelta(days=30)).isoformat()
    data = {
        "description": "Test UUID with expiration",
        "expires_at": future_date
    }
    
    response = requests.post(f"{BASE_URL}/admin/uuids", headers=headers, json=data)
    print(f"Status: {response.status_code}")
    result = response.json()
    print(f"Created UUID: {result['uuid']}")
    print(f"Description: {result['description']}")
    print(f"Expires: {result['expires_at']}")
    print()
    return result['uuid']

def test_uuid_validation(uuid_value):
    """Test UUID validation (public endpoint)"""
    print("✅ Testing UUID validation...")
    
    # First validation - should activate
    print("First validation (should activate):")
    response = requests.get(f"{BASE_URL}/validate/{uuid_value}")
    print(f"Status: {response.status_code}")
    result = response.json()
    print(f"Valid: {result['is_valid']}")
    print(f"Activated: {result['is_activated']}")
    print(f"Activated now: {result['activated_now']}")
    print(f"Message: {result['message']}")
    print()
    
    # Second validation - should not activate again
    print("Second validation (should not activate again):")
    response = requests.get(f"{BASE_URL}/validate/{uuid_value}")
    result = response.json()
    print(f"Valid: {result['is_valid']}")
    print(f"Activated: {result['is_activated']}")
    print(f"Activated now: {result['activated_now']}")
    print(f"Message: {result['message']}")
    print()

def test_uuid_status(uuid_value):
    """Test UUID status check"""
    print("📊 Testing UUID status check...")
    response = requests.get(f"{BASE_URL}/status/{uuid_value}")
    print(f"Status: {response.status_code}")
    result = response.json()
    print(f"UUID: {result['uuid']}")
    print(f"Status: {result['status']}")
    print(f"Valid: {result['is_valid']}")
    print(f"Activated: {result['is_activated']}")
    print(f"Usage count: {result['usage_count']}")
    print(f"Created: {result['created_at']}")
    print(f"Last used: {result['last_used_at']}")
    print(f"Activated at: {result['activated_at']}")
    print()

def test_list_uuids(token):
    """Test listing UUIDs"""
    print("📋 Testing UUID listing...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/admin/uuids", headers=headers)
    print(f"Status: {response.status_code}")
    result = response.json()
    print(f"Total UUIDs: {result['total']}")
    for uuid_data in result['uuids']:
        print(f"  - {uuid_data['uuid']} ({uuid_data['status']}, activated: {uuid_data['is_activated']})")
    print()

def test_uuid_stats(token):
    """Test UUID statistics"""
    print("📈 Testing UUID statistics...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/admin/stats", headers=headers)
    print(f"Status: {response.status_code}")
    result = response.json()
    print(f"Total UUIDs: {result['total_uuids']}")
    print(f"Active UUIDs: {result['active_uuids']}")
    print(f"Activated UUIDs: {result['activated_uuids']}")
    print(f"Total usage: {result['total_usage']}")
    print()

def test_invalid_uuid():
    """Test validation of non-existent UUID"""
    print("❌ Testing invalid UUID...")
    fake_uuid = "00000000-0000-0000-0000-000000000000"
    response = requests.get(f"{BASE_URL}/validate/{fake_uuid}")
    print(f"Status: {response.status_code}")
    if response.status_code == 404:
        print("✅ Correctly returned 404 for non-existent UUID")
    print()

def main():
    """Run all tests"""
    print("🚀 Starting UUID Management System API Tests\n")
    
    try:
        # Test basic functionality
        test_health()
        
        # Test authentication
        token = test_admin_login()
        
        # Test UUID creation
        uuid_value = test_create_uuid(token)
        
        # Test UUID validation and activation
        test_uuid_validation(uuid_value)
        
        # Test status checking
        test_uuid_status(uuid_value)
        
        # Test admin endpoints
        test_list_uuids(token)
        test_uuid_stats(token)
        
        # Test error handling
        test_invalid_uuid()
        
        print("✅ All tests completed successfully!")
        print("\n🌐 You can also test the web interface at: http://localhost:8000")
        print("   Login with: admin / admin123")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
