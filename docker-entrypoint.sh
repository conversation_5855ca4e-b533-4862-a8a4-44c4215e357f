#!/bin/bash
set -e

# Docker entrypoint script for UUID Management System

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} ✅ $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} ⚠️  $1"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} ❌ $1"
}

# Function to wait for database (if using PostgreSQL)
wait_for_db() {
    if [[ "$DATABASE_URL" == postgresql* ]]; then
        log "Waiting for PostgreSQL database to be ready..."
        
        # Extract host and port from DATABASE_URL
        DB_HOST=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
        DB_PORT=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
        
        if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ]; then
            log_warning "Could not parse database host/port from DATABASE_URL"
            return
        fi
        
        # Wait for database to be ready
        for i in {1..30}; do
            if python -c "
import psycopg2
import sys
try:
    conn = psycopg2.connect('$DATABASE_URL')
    conn.close()
    sys.exit(0)
except:
    sys.exit(1)
" 2>/dev/null; then
                log_success "Database is ready!"
                return
            fi
            log "Waiting for database... (attempt $i/30)"
            sleep 2
        done
        
        log_error "Database is not ready after 60 seconds"
        exit 1
    else
        log "Using SQLite database, no wait required"
    fi
}

# Function to initialize database
init_database() {
    log "Initializing database..."
    
    # Run database initialization
    python -c "
from app.database import init_db
try:
    init_db()
    print('✅ Database initialized successfully')
except Exception as e:
    print(f'❌ Database initialization failed: {e}')
    exit(1)
"
    
    if [ $? -eq 0 ]; then
        log_success "Database initialization completed"
    else
        log_error "Database initialization failed"
        exit 1
    fi
}

# Function to run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Check if migration script exists and run it
    if [ -f "migrate_db.py" ]; then
        python migrate_db.py
        if [ $? -eq 0 ]; then
            log_success "Database migrations completed"
        else
            log_warning "Database migrations had issues (this might be normal for new installations)"
        fi
    else
        log "No migration script found, skipping migrations"
    fi
}

# Function to validate environment
validate_environment() {
    log "Validating environment configuration..."
    
    # Check required environment variables
    if [ -z "$SECRET_KEY" ] || [ "$SECRET_KEY" = "your-super-secret-key-change-this-in-production" ]; then
        log_warning "SECRET_KEY is not set or using default value. This is insecure for production!"
    fi
    
    if [ "$DEBUG" = "true" ]; then
        log_warning "DEBUG mode is enabled. Disable for production!"
    fi
    
    # Validate database URL
    if [ -z "$DATABASE_URL" ]; then
        log "DATABASE_URL not set, using default SQLite"
        export DATABASE_URL="sqlite:///./data/uuid_management.db"
    fi
    
    log_success "Environment validation completed"
}

# Function to create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    # Create data directory for SQLite if needed
    if [[ "$DATABASE_URL" == sqlite* ]]; then
        mkdir -p /app/data
        log "Created data directory for SQLite"
    fi
    
    # Create logs directory
    mkdir -p /app/logs
    log "Created logs directory"
    
    # Ensure static directories exist
    mkdir -p /app/static/css /app/static/js
    log "Ensured static directories exist"
    
    log_success "Directory creation completed"
}

# Function to handle graceful shutdown
cleanup() {
    log "Received shutdown signal, cleaning up..."
    # Add any cleanup logic here
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Main execution
main() {
    log "🚀 Starting UUID Management System..."
    log "Platform: $(uname -m)"
    log "Python version: $(python --version)"
    
    # Validate environment
    validate_environment
    
    # Create directories
    create_directories
    
    # Wait for database if needed
    wait_for_db
    
    # Run migrations
    run_migrations
    
    # Initialize database
    init_database
    
    log_success "Initialization completed successfully!"
    log "🌟 Starting application with command: $@"
    
    # Execute the main command
    exec "$@"
}

# Run main function
main "$@"
