version: '3.8'

# Production overrides for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

services:
  uuid-management:
    environment:
      # Production Database Configuration (PostgreSQL)
      DATABASE_URL: **************************************************/uuid_management
      
      # Production Security Configuration
      SECRET_KEY: ${SECRET_KEY}
      ALGORITHM: HS256
      ACCESS_TOKEN_EXPIRE_MINUTES: 60
      
      # Production Admin Configuration
      ADMIN_USERNAME: ${ADMIN_USERNAME}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD}
      
      # Production Application Configuration
      APP_NAME: UUID Management System
      APP_VERSION: 1.0.0
      DEBUG: "false"
      
      # Production Python Configuration
      PYTHONPATH: /app
      PYTHONUNBUFFERED: "1"
      PYTHONDONTWRITEBYTECODE: "1"
    
    # Remove port mapping (nginx will handle this)
    ports: []
    
    # Production restart policy
    restart: always
    
    # Resource limits for production
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    
    # Production health check with longer intervals
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    depends_on:
      postgres:
        condition: service_healthy

  postgres:
    environment:
      POSTGRES_DB: uuid_management
      POSTGRES_USER: ${POSTGRES_USER:-uuid_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    
    # Production restart policy
    restart: always
    
    # Remove port mapping for security
    ports: []
    
    # Production resource limits
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    
    # Production PostgreSQL configuration
    command: >
      postgres
      -c max_connections=100
      -c shared_buffers=128MB
      -c effective_cache_size=256MB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB

  nginx:
    # Production restart policy
    restart: always
    
    # Production resource limits
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 64M
        reservations:
          cpus: '0.1'
          memory: 32M
    
    # Production health check
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# Production profiles - enable postgres and nginx by default
profiles:
  postgres: []
  nginx: []
