#!/usr/bin/env python3
"""
Simple test to check server connectivity and basic functionality
"""

import requests
import time

BASE_URL = "http://127.0.0.1:8000"

def test_server_connectivity():
    """Test if server is responding"""
    print("🌐 Testing server connectivity...")
    
    try:
        # Try to access the dashboard
        response = requests.get(f"{BASE_URL}/dashboard", timeout=5)
        if response.status_code == 200:
            print("   ✅ Server is responding")
            return True
        else:
            print(f"   ❌ Server responded with status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to server")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_admin_login():
    """Test admin login"""
    print("🔑 Testing admin login...")
    
    try:
        data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{BASE_URL}/admin/login", data=data, timeout=10)
        
        if response.status_code == 200:
            token = response.json()['access_token']
            print("   ✅ Login successful")
            return token
        else:
            print(f"   ❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return None

def test_admin_settings(token):
    """Test admin settings endpoint"""
    print("⚙️ Testing admin settings...")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/admin/settings", headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Admin settings working")
            print(f"   User: {data['user']['username']}")
            return True
        else:
            print(f"   ❌ Admin settings failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Admin settings error: {e}")
        return False

def test_password_change(token):
    """Test password change"""
    print("🔄 Testing password change...")
    
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # Try to change password
        new_password = f"TestPass{int(time.time())}!"
        data = {
            "current_password": "admin123",
            "new_password": new_password,
            "confirm_password": new_password
        }
        
        response = requests.post(
            f"{BASE_URL}/admin/change-password",
            headers=headers,
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            print("   ✅ Password change successful")
            
            # Test login with new password
            login_data = {"username": "admin", "password": new_password}
            login_response = requests.post(f"{BASE_URL}/admin/login", data=login_data, timeout=10)
            
            if login_response.status_code == 200:
                print("   ✅ Login with new password successful")
                new_token = login_response.json()['access_token']
                
                # Change back to original password
                reset_data = {
                    "current_password": new_password,
                    "new_password": "admin123",
                    "confirm_password": "admin123"
                }
                
                reset_headers = {
                    "Authorization": f"Bearer {new_token}",
                    "Content-Type": "application/json"
                }
                
                reset_response = requests.post(
                    f"{BASE_URL}/admin/change-password",
                    headers=reset_headers,
                    json=reset_data,
                    timeout=10
                )
                
                if reset_response.status_code == 200:
                    print("   ✅ Password reset successful")
                    return True
                else:
                    print(f"   ⚠️ Password reset failed: {reset_response.status_code}")
                    return False
            else:
                print(f"   ❌ Login with new password failed: {login_response.status_code}")
                return False
        else:
            print(f"   ❌ Password change failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Password change error: {e}")
        return False

def main():
    """Run simple tests"""
    print("🧪 Simple Server Tests")
    print("=" * 30)
    
    # Test server connectivity
    if not test_server_connectivity():
        print("❌ Server is not running or not accessible")
        return False
    
    # Test admin login
    token = test_admin_login()
    if not token:
        print("❌ Cannot authenticate")
        return False
    
    # Test admin settings
    if not test_admin_settings(token):
        print("❌ Admin settings not working")
        return False
    
    # Test password change
    if not test_password_change(token):
        print("❌ Password change not working")
        return False
    
    print("\n✅ All simple tests passed!")
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
