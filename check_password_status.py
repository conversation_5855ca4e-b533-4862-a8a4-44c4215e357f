#!/usr/bin/env python3
"""
检查当前密码状态和要求
"""

import requests

BASE_URL = "http://127.0.0.1:8000"

def check_current_password():
    """检查当前密码"""
    print("🔍 检查当前管理员密码状态")
    print("=" * 40)
    
    # 尝试不同的密码
    passwords_to_try = [
        "admin123",
        "admin123!",
        "Admin123!",
        "password",
        "admin"
    ]
    
    for password in passwords_to_try:
        print(f"尝试密码: {password}")
        
        try:
            data = {"username": "admin", "password": password}
            response = requests.post(f"{BASE_URL}/admin/login", data=data, timeout=5)
            
            if response.status_code == 200:
                print(f"   ✅ 成功！当前密码是: {password}")
                token = response.json()['access_token']
                return password, token
            else:
                print(f"   ❌ 失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
    
    print("❌ 无法确定当前密码")
    return None, None

def check_password_requirements():
    """检查密码要求"""
    print("\n📋 检查密码要求")
    print("=" * 30)
    
    from app.schemas import PasswordChangeRequest
    
    # 测试不同的密码
    test_passwords = [
        ("admin123", "默认密码"),
        ("admin123!", "默认密码+特殊字符"),
        ("Admin123!", "大写+数字+特殊字符"),
        ("NewPass123!", "新密码示例")
    ]
    
    for password, description in test_passwords:
        print(f"\n测试密码: {password} ({description})")
        
        # 创建一个测试请求
        request = PasswordChangeRequest(
            current_password="dummy",  # 这里用假的当前密码，只测试新密码要求
            new_password=password,
            confirm_password=password
        )
        
        is_valid, errors = request.validate_password_strength()
        
        if is_valid:
            print("   ✅ 符合要求")
        else:
            print("   ❌ 不符合要求:")
            for error in errors:
                if "different from current" not in error:  # 忽略与当前密码相同的错误
                    print(f"      - {error}")

def get_admin_info(token):
    """获取管理员信息"""
    print("\n👤 获取管理员信息")
    print("=" * 25)
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/admin/settings", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            user = data['user']
            
            print(f"用户名: {user['username']}")
            print(f"用户ID: {user['id']}")
            print(f"是否管理员: {user['is_admin']}")
            print(f"是否激活: {user['is_active']}")
            print(f"创建时间: {user['created_at']}")
            print(f"最后登录: {user['last_login_at']}")
            print(f"密码修改时间: {user['password_changed_at']}")
            print(f"失败尝试次数: {user['failed_login_attempts']}")
            print(f"是否锁定: {user['is_locked']}")
            
            return user
        else:
            print(f"❌ 获取信息失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

def main():
    """主函数"""
    print("🔐 密码状态检查工具")
    print("=" * 50)
    
    # 检查当前密码
    current_password, token = check_current_password()
    
    if current_password and token:
        print(f"\n✅ 当前管理员密码: {current_password}")
        
        # 获取管理员信息
        get_admin_info(token)
    else:
        print("\n❌ 无法确定当前密码")
    
    # 检查密码要求
    check_password_requirements()
    
    print(f"\n🌐 管理界面地址: {BASE_URL}/admin/settings-page")

if __name__ == "__main__":
    main()
