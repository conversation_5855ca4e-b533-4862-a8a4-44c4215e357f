{% extends "base.html" %}

{% block title %}Manage UUIDs - UUID Management System{% endblock %}

{% block content %}
<!-- Enhanced Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-header-main">
            <h1 class="page-title">
                <i class="fas fa-list-ul page-title-icon"></i>
                Manage UUIDs
            </h1>
            <p class="page-subtitle">Create, edit, and monitor your UUID collection</p>
            <div class="page-meta">
                <div class="page-meta-item">
                    <i class="fas fa-layer-group"></i>
                    <span>Total UUIDs: <span id="headerUuidCount">{{ uuids|length }}</span></span>
                </div>
                <div class="page-meta-separator"></div>
                <div class="page-meta-item">
                    <i class="fas fa-sync-alt"></i>
                    <span>Auto-refresh: <span style="color: var(--color-success);">Enabled</span></span>
                </div>
                <div class="page-meta-separator"></div>
                <div class="page-meta-item">
                    <i class="fas fa-filter"></i>
                    <span id="filterStatus">All UUIDs</span>
                </div>
            </div>
        </div>
        <div class="page-header-actions">
            <button class="btn-apple btn-apple-primary btn-apple-lg" onclick="createNewUUID()">
                <i class="fas fa-plus"></i>
                Create New UUID
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card-apple" style="margin-bottom: var(--spacing-2xl);">
    <div class="card-apple-header">
        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
            <i class="fas fa-filter" style="color: var(--color-primary);"></i>
            Filters
        </h3>
    </div>
    <div class="card-apple-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md); align-items: end;">
            <div class="form-group-apple" style="margin-bottom: 0;">
                <label for="statusFilter" class="form-label-apple">Status</label>
                <select class="form-input-apple" id="statusFilter" onchange="filterUUIDs()">
                    <option value="">All Statuses</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="expired">Expired</option>
                </select>
            </div>
            <div class="form-group-apple" style="margin-bottom: 0;">
                <label for="activationFilter" class="form-label-apple">Activation</label>
                <select class="form-input-apple" id="activationFilter" onchange="filterUUIDs()">
                    <option value="">All UUIDs</option>
                    <option value="true">Activated</option>
                    <option value="false">Not Activated</option>
                </select>
            </div>
            <div class="form-group-apple" style="margin-bottom: 0;">
                <label for="searchInput" class="form-label-apple">Search</label>
                <input
                    type="text"
                    class="form-input-apple"
                    id="searchInput"
                    placeholder="Search UUIDs..."
                    onkeyup="filterUUIDs()"
                >
            </div>
            <div style="display: flex; gap: var(--spacing-sm);">
                <button class="btn-apple btn-apple-secondary" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                    Clear
                </button>
                <button class="btn-apple btn-apple-ghost" onclick="refreshUUIDs()" title="Refresh">
                    <i class="fas fa-refresh"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- UUIDs Table -->
<div class="card-apple">
    <div class="card-apple-header">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--spacing-md);">
            <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                <i class="fas fa-list-ul" style="color: var(--color-primary);"></i>
                UUID Collection
            </h3>
            <div style="display: flex; align-items: center; gap: var(--spacing-sm); color: var(--color-text-secondary); font-size: var(--font-size-sm);">
                <span id="uuidCount">{{ uuids|length }} UUIDs</span>
                <span>•</span>
                <span id="filteredCount" style="display: none;"></span>
                <span id="filterStatus" style="display: none;"></span>
                <span>•</span>
                <button
                    class="btn-apple btn-apple-ghost btn-apple-sm"
                    onclick="manualRefreshUsageCounts()"
                    id="refreshUsageBtn"
                    title="Refresh usage counts"
                    style="padding: var(--spacing-xs) var(--spacing-sm); font-size: var(--font-size-xs);"
                >
                    <i class="fas fa-sync-alt"></i>
                    <span style="margin-left: var(--spacing-xs);">Refresh</span>
                </button>
                <span>•</span>
                <div id="realTimeStatus" style="display: flex; align-items: center; gap: var(--spacing-xs); font-size: var(--font-size-xs);">
                    <div id="statusIndicator" style="width: 8px; height: 8px; border-radius: 50%; background: var(--color-success); animation: pulse 2s infinite;"></div>
                    <span>Live Updates</span>
                </div>
            </div>
        </div>
    </div>
    <div class="card-apple-body">
        {% if uuids %}
        <div id="uuidsContainer" class="fade-in-up">
            {% for uuid in uuids %}
            <div class="uuid-card-row" data-uuid="{{ uuid.uuid }}" data-status="{{ uuid.status }}" data-activated="{{ uuid.is_activated|lower }}">
                <div class="uuid-card-header">
                    <div class="uuid-card-main">
                        <!-- UUID Display with Copy Functionality -->
                        <div class="uuid-display" onclick="toggleUUIDExpansion(this)" title="Click to expand/collapse">
                            <span class="uuid-text">{{ uuid.uuid }}</span>
                            <i class="fas fa-chevron-down expand-icon"></i>
                            <i class="fas fa-copy copy-icon" onclick="copyUUID(event, '{{ uuid.uuid }}')" title="Copy UUID"></i>
                        </div>

                        {% if uuid.description %}
                        <div style="margin-top: var(--spacing-sm); color: var(--color-text-secondary); font-size: var(--font-size-sm); font-weight: var(--font-weight-medium);">
                            {{ uuid.description }}
                        </div>
                        {% endif %}

                        <!-- Status Badges -->
                        <div class="status-badge-group" style="margin-top: var(--spacing-sm);">
                            <span class="status-badge-{{ 'success' if uuid.status == 'active' else 'danger' if uuid.status == 'expired' else 'secondary' }}">
                                <i class="fas fa-{{ 'check-circle' if uuid.status == 'active' else 'times-circle' if uuid.status == 'expired' else 'pause-circle' }}"></i>
                                {{ uuid.status.title() }}
                            </span>

                            {% if uuid.is_expired and uuid.status != 'expired' %}
                            <span class="status-badge-danger">
                                <i class="fas fa-clock"></i>
                                Expired
                            </span>
                            {% endif %}

                            {% if uuid.is_activated %}
                            <span class="status-badge-warning">
                                <i class="fas fa-star"></i>
                                Activated
                            </span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-button-group">
                        <button class="action-btn" onclick="editUUID('{{ uuid.uuid }}')" title="Edit UUID">
                            <i class="fas fa-edit"></i>
                        </button>

                        {% if not uuid.is_activated and uuid.status == 'active' and not uuid.is_expired %}
                        <button class="action-btn success" onclick="activateUUID('{{ uuid.uuid }}')" title="Activate UUID">
                            <i class="fas fa-star"></i>
                        </button>
                        {% endif %}

                        <button class="action-btn primary" onclick="testUUID('{{ uuid.uuid }}')" title="Test UUID">
                            <i class="fas fa-vial"></i>
                        </button>

                        <button class="action-btn danger" onclick="deleteUUID('{{ uuid.uuid }}')" title="Delete UUID">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- UUID Metadata -->
                <div class="uuid-card-meta">
                    <div class="uuid-meta-item">
                        <div class="uuid-meta-label">Created</div>
                        <div class="uuid-meta-value">
                            {{ uuid.created_at.strftime('%Y-%m-%d') }}
                            <div style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); margin-top: 2px;">
                                {{ uuid.created_at.strftime('%H:%M') }}
                            </div>
                        </div>
                    </div>

                    <div class="uuid-meta-item">
                        <div class="uuid-meta-label">Expires</div>
                        <div class="uuid-meta-value">
                            {% if uuid.expires_at %}
                            {{ uuid.expires_at.strftime('%Y-%m-%d') }}
                            <div style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); margin-top: 2px;">
                                {{ uuid.expires_at.strftime('%H:%M') }}
                            </div>
                            {% else %}
                            <span style="color: var(--color-text-tertiary);">Never</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="uuid-meta-item">
                        <div class="uuid-meta-label">Usage Count</div>
                        <div class="uuid-meta-value">
                            <span class="status-badge-secondary">{{ uuid.usage_count }}</span>
                            {% if uuid.last_used_at %}
                            <div style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); margin-top: 4px;">
                                Last: {{ uuid.last_used_at.strftime('%m-%d %H:%M') }}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    {% if uuid.is_activated %}
                    <div class="uuid-meta-item">
                        <div class="uuid-meta-label">Activation</div>
                        <div class="uuid-meta-value">
                            {% if uuid.activated_at %}
                            {{ uuid.activated_at.strftime('%m-%d %H:%M') }}
                            {% endif %}
                            {% if uuid.activation_method %}
                            <div style="font-size: var(--font-size-xs); color: var(--color-text-tertiary); margin-top: 2px;">
                                via {{ uuid.activation_method }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-layer-group"></i>
            </div>
            <h3 class="empty-state-title">No UUIDs Found</h3>
            <p class="empty-state-description">
                Your UUID collection is empty. Create your first UUID to get started with managing unique identifiers for your applications.
            </p>
            <button class="btn-apple btn-apple-primary" onclick="createNewUUID()">
                <i class="fas fa-plus"></i>
                Create First UUID
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Create/Edit UUID Modal -->
<div class="modal-apple" id="uuidModal">
    <div class="modal-apple-content">
        <div class="modal-apple-header">
            <h3 class="modal-apple-title" id="modalTitle">Create New UUID</h3>
            <button type="button" class="modal-apple-close" onclick="closeUUIDModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-apple-body">
            <form id="uuidForm">
                <input type="hidden" id="editingUUID" value="">
                <div class="form-group-apple">
                    <label for="description" class="form-label-apple">Description (Optional)</label>
                    <textarea
                        class="form-input-apple"
                        id="description"
                        name="description"
                        rows="3"
                        placeholder="Enter a description for this UUID..."
                        style="resize: vertical; min-height: 80px;"
                    ></textarea>
                </div>
                <div class="form-group-apple">
                    <label for="expires_at" class="form-label-apple">Expiration Date (Optional)</label>
                    <input
                        type="datetime-local"
                        class="form-input-apple"
                        id="expires_at"
                        name="expires_at"
                    >
                </div>
                <div class="form-group-apple" id="statusGroup" style="display: none;">
                    <label for="status" class="form-label-apple">Status</label>
                    <select class="form-input-apple" id="status" name="status">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </form>
        </div>
        <div class="modal-apple-footer">
            <button type="button" class="btn-apple btn-apple-secondary" onclick="closeUUIDModal()">
                Cancel
            </button>
            <button type="button" class="btn-apple btn-apple-primary" onclick="submitUUID()" id="submitUUIDButton">
                <i class="fas fa-plus"></i>
                Create UUID
            </button>
        </div>
    </div>
</div>

<!-- Test UUID Modal -->
<div class="modal-apple" id="testModal">
    <div class="modal-apple-content">
        <div class="modal-apple-header">
            <h3 class="modal-apple-title">Test UUID</h3>
            <button type="button" class="modal-apple-close" onclick="closeTestModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-apple-body">
            <div id="testResults"></div>
        </div>
        <div class="modal-apple-footer">
            <button type="button" class="btn-apple btn-apple-secondary" onclick="closeTestModal()">
                Close
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentModal;

// Enhanced UUID Display Functions
function toggleUUIDExpansion(element) {
    element.classList.toggle('expanded');

    // Add animation class
    element.classList.add('fade-in-up');
    setTimeout(() => {
        element.classList.remove('fade-in-up');
    }, 400);
}

function copyUUID(event, uuid) {
    event.stopPropagation(); // Prevent expansion toggle

    navigator.clipboard.writeText(uuid).then(() => {
        const copyIcon = event.target;
        const originalClass = copyIcon.className;

        // Show success feedback
        copyIcon.className = 'fas fa-check';
        copyIcon.style.color = 'var(--color-success)';

        // Add success animation to parent
        const uuidDisplay = copyIcon.closest('.uuid-display');
        uuidDisplay.classList.add('uuid-copy-success');

        // Show notification
        showNotification(`UUID copied to clipboard: ${uuid.substring(0, 8)}...`, 'success');

        // Reset after delay
        setTimeout(() => {
            copyIcon.className = originalClass;
            copyIcon.style.color = '';
            uuidDisplay.classList.remove('uuid-copy-success');
        }, 1000);
    }).catch(err => {
        showNotification('Failed to copy UUID', 'error');
        console.error('Failed to copy: ', err);
    });
}

function createNewUUID() {
    document.getElementById('modalTitle').textContent = 'Create New UUID';
    document.getElementById('editingUUID').value = '';
    document.getElementById('uuidForm').reset();
    document.getElementById('statusGroup').style.display = 'none';

    // Update button text for create mode
    const submitButton = document.getElementById('submitUUIDButton');
    submitButton.innerHTML = '<i class="fas fa-plus"></i> Create UUID';

    const modal = document.getElementById('uuidModal');
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function editUUID(uuid) {
    document.getElementById('modalTitle').textContent = 'Edit UUID';
    document.getElementById('editingUUID').value = uuid;
    document.getElementById('statusGroup').style.display = 'block';

    // Update button text for edit mode
    const submitButton = document.getElementById('submitUUIDButton');
    submitButton.innerHTML = '<i class="fas fa-save"></i> Save Changes';

    // Load current UUID data
    loadUUIDData(uuid);

    const modal = document.getElementById('uuidModal');
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function closeUUIDModal() {
    const modal = document.getElementById('uuidModal');
    modal.classList.remove('show');
    document.body.style.overflow = '';

    // Reset form
    document.getElementById('uuidForm').reset();
}

function closeTestModal() {
    const modal = document.getElementById('testModal');
    modal.classList.remove('show');
    document.body.style.overflow = '';
}

async function loadUUIDData(uuid) {
    try {
        const response = await fetch(`/admin/uuids/${uuid}`, {
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            const data = await response.json();
            document.getElementById('description').value = data.description || '';
            document.getElementById('status').value = data.status;
            
            if (data.expires_at) {
                // Convert to local datetime format
                const date = new Date(data.expires_at);
                document.getElementById('expires_at').value = date.toISOString().slice(0, 16);
            }
        }
    } catch (error) {
        console.error('Error loading UUID data:', error);
    }
}

async function submitUUID() {
    const form = document.getElementById('uuidForm');
    const formData = new FormData(form);
    const editingUUID = document.getElementById('editingUUID').value;
    const button = document.getElementById('submitUUIDButton');

    const data = {
        description: formData.get('description') || null,
        expires_at: formData.get('expires_at') || null
    };

    if (editingUUID) {
        data.status = formData.get('status');
    }

    // Show loading state
    const originalContent = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<div class="loading-apple"></div> Processing...';

    try {
        const url = editingUUID ? `/admin/uuids/${editingUUID}` : '/admin/uuids';
        const method = editingUUID ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeaders()
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const result = await response.json();

            // Show success state
            button.innerHTML = '<i class="fas fa-check"></i> Success!';
            button.style.background = 'var(--color-success)';

            const action = editingUUID ? 'updated' : 'created';
            const uuidShort = result.uuid ? result.uuid.substring(0, 8) + '...' : '';
            showNotification(`UUID ${action} successfully: ${uuidShort}`, 'success');

            // Close modal and reload after delay
            setTimeout(() => {
                closeUUIDModal();
                location.reload();
            }, 1000);
        } else {
            const error = await response.json();
            showNotification(`Error: ${error.detail}`, 'error');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalContent;
            button.style.background = '';
        }
    } catch (error) {
        showNotification(`Error: ${error.message}`, 'error');

        // Reset button
        button.disabled = false;
        button.innerHTML = originalContent;
        button.style.background = '';
    }
}

async function deleteUUID(uuid) {
    if (!confirm('Are you sure you want to delete this UUID? This action cannot be undone.')) {
        return;
    }

    const button = event.target.closest('.action-btn');
    const uuidCard = button.closest('.uuid-card-row');
    const originalContent = button.innerHTML;

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<div class="loading-spinner"></div>';

    try {
        const response = await fetch(`/admin/uuids/${uuid}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });

        if (response.ok) {
            // Animate card removal
            uuidCard.style.transform = 'translateX(-100%)';
            uuidCard.style.opacity = '0';

            showNotification(`UUID deleted successfully: ${uuid.substring(0, 8)}...`, 'success');

            // Remove from DOM after animation
            setTimeout(() => {
                uuidCard.remove();

                // Check if no UUIDs left
                const container = document.getElementById('uuidsContainer');
                if (container && container.children.length === 0) {
                    location.reload();
                }
            }, 300);
        } else {
            const error = await response.json();
            showNotification(`Delete failed: ${error.detail}`, 'error');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    } catch (error) {
        showNotification(`Delete error: ${error.message}`, 'error');

        // Reset button
        button.disabled = false;
        button.innerHTML = originalContent;
    }
}

async function testUUID(uuid) {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div style="text-align: center; padding: var(--spacing-lg);"><div class="loading-apple"></div><p style="margin-top: var(--spacing-md); color: var(--color-text-secondary);">Testing UUID...</p></div>';

    const modal = document.getElementById('testModal');
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';

    try {
        // Test validation endpoint
        const validateResponse = await fetch(`/validate/${uuid}`, {
            headers: getAuthHeaders()
        });
        const validateData = await validateResponse.json();

        // Test status endpoint
        const statusResponse = await fetch(`/status/${uuid}`, {
            headers: getAuthHeaders()
        });
        const statusData = await statusResponse.json();

        resultsDiv.innerHTML = `
            <div style="margin-bottom: var(--spacing-lg);">
                <h4 style="color: var(--color-text-primary); margin-bottom: var(--spacing-md); display: flex; align-items: center; gap: var(--spacing-sm);">
                    <i class="fas fa-vial" style="color: var(--color-primary);"></i>
                    Validation Test
                </h4>
                <div class="card-apple" style="background: ${validateResponse.ok ? 'var(--color-success-light)' : 'var(--color-danger-light)'}; border: 1px solid ${validateResponse.ok ? 'var(--color-success)' : 'var(--color-danger)'};">
                    <div class="card-apple-body" style="padding: var(--spacing-md);">
                        <div style="display: grid; gap: var(--spacing-sm); font-size: var(--font-size-sm);">
                            <div><strong>Status:</strong> ${validateResponse.status}</div>
                            <div><strong>Message:</strong> ${validateData.message || validateData.detail}</div>
                            <div><strong>Valid:</strong> <span class="badge-apple badge-apple-${validateData.is_valid ? 'success' : 'danger'}">${validateData.is_valid}</span></div>
                            <div><strong>Activated:</strong> <span class="badge-apple badge-apple-${validateData.is_activated ? 'warning' : 'secondary'}">${validateData.is_activated}</span></div>
                            <div><strong>Activated Now:</strong> <span class="badge-apple badge-apple-${validateData.activated_now ? 'warning' : 'secondary'}">${validateData.activated_now}</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <h4 style="color: var(--color-text-primary); margin-bottom: var(--spacing-md); display: flex; align-items: center; gap: var(--spacing-sm);">
                    <i class="fas fa-info-circle" style="color: var(--color-primary);"></i>
                    Status Information
                </h4>
                <div class="card-apple" style="background: var(--color-gray-50); border: 1px solid var(--color-gray-200);">
                    <div class="card-apple-body" style="padding: var(--spacing-md);">
                        <div style="display: grid; gap: var(--spacing-sm); font-size: var(--font-size-sm);">
                            <div><strong>Status:</strong> <span class="badge-apple badge-apple-${statusData.status === 'active' ? 'success' : 'secondary'}">${statusData.status}</span></div>
                            <div><strong>Usage Count:</strong> <span class="badge-apple badge-apple-secondary">${statusData.usage_count}</span></div>
                            <div><strong>Valid:</strong> <span class="badge-apple badge-apple-${statusData.is_valid ? 'success' : 'danger'}">${statusData.is_valid}</span></div>
                            <div><strong>Activated:</strong> <span class="badge-apple badge-apple-${statusData.is_activated ? 'warning' : 'secondary'}">${statusData.is_activated}</span></div>
                            <div><strong>Expired:</strong> <span class="badge-apple badge-apple-${statusData.is_expired ? 'danger' : 'success'}">${statusData.is_expired}</span></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Show success notification
        showNotification(`UUID test completed for ${uuid.substring(0, 8)}...`, 'success');

    } catch (error) {
        resultsDiv.innerHTML = `
            <div class="card-apple" style="background: var(--color-danger-light); border: 1px solid var(--color-danger);">
                <div class="card-apple-body" style="padding: var(--spacing-md);">
                    <div style="display: flex; align-items: center; gap: var(--spacing-sm); color: var(--color-danger);">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Test Error:</strong> ${error.message}
                    </div>
                </div>
            </div>
        `;
        showNotification(`Test error: ${error.message}`, 'error');
    }
}

async function activateUUID(uuid) {
    const button = event.target.closest('.action-btn');
    const originalContent = button.innerHTML;

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<div class="loading-spinner"></div>';

    try {
        const response = await fetch(`/admin/uuids/${uuid}/activate`, {
            method: 'POST',
            headers: getAuthHeaders()
        });

        if (response.ok) {
            const result = await response.json();

            // Show success state
            button.innerHTML = '<i class="fas fa-star"></i>';
            button.style.background = 'var(--color-orange)';
            button.style.color = 'var(--color-white)';

            showNotification(`UUID activated successfully: ${result.message}`, 'success');

            // Update the card to show activated state
            setTimeout(() => {
                location.reload(); // Reload to show updated state
            }, 1000);
        } else {
            const error = await response.json();
            showNotification(`Activation failed: ${error.detail}`, 'error');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalContent;
        }
    } catch (error) {
        showNotification(`Activation error: ${error.message}`, 'error');

        // Reset button
        button.disabled = false;
        button.innerHTML = originalContent;
    }
}

function filterUUIDs() {
    const statusFilter = document.getElementById('statusFilter').value;
    const activationFilter = document.getElementById('activationFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();

    // 更新为使用新的卡片布局选择器
    const cards = document.querySelectorAll('.uuid-card-row');
    let visibleCount = 0;
    let totalCount = cards.length;

    cards.forEach(card => {
        const uuid = card.dataset.uuid.toLowerCase();
        const status = card.dataset.status;
        const activated = card.dataset.activated;

        let show = true;

        if (statusFilter && status !== statusFilter) {
            show = false;
        }

        if (activationFilter && activated !== activationFilter) {
            show = false;
        }

        if (searchInput && !uuid.includes(searchInput)) {
            show = false;
        }

        card.style.display = show ? '' : 'none';
        if (show) visibleCount++;
    });

    // 更新计数显示
    updateFilterCount(visibleCount, totalCount, statusFilter, activationFilter, searchInput);
}

function updateFilterCount(visibleCount, totalCount, statusFilter, activationFilter, searchInput) {
    const uuidCountElement = document.getElementById('uuidCount');
    const filteredCountElement = document.getElementById('filteredCount');
    const filterStatusElement = document.getElementById('filterStatus');
    const headerUuidCount = document.getElementById('headerUuidCount');
    const headerFilterStatus = document.getElementById('filterStatus');

    // Update header count
    if (headerUuidCount) {
        headerUuidCount.textContent = totalCount;
    }

    if (visibleCount === totalCount) {
        // 没有过滤，显示总数
        uuidCountElement.textContent = `${totalCount} UUIDs`;
        filteredCountElement.style.display = 'none';
        filterStatusElement.style.display = 'none';

        // Update header filter status
        if (headerFilterStatus) {
            headerFilterStatus.textContent = 'All UUIDs';
        }
    } else {
        // 有过滤，显示过滤后的数量
        uuidCountElement.textContent = `${totalCount} UUIDs`;
        filteredCountElement.textContent = `${visibleCount} shown`;
        filteredCountElement.style.display = 'inline';

        // 显示过滤状态
        const filters = [];
        if (statusFilter) filters.push(`Status: ${statusFilter}`);
        if (activationFilter) filters.push(`Activation: ${activationFilter}`);
        if (searchInput) filters.push(`Search: "${searchInput}"`);

        if (filters.length > 0) {
            filterStatusElement.textContent = `(${filters.join(', ')})`;
            filterStatusElement.style.display = 'inline';

            // Update header filter status
            if (headerFilterStatus) {
                headerFilterStatus.innerHTML = `<span style="color: var(--color-warning);">${visibleCount} of ${totalCount} UUIDs</span>`;
            }
        } else {
            filterStatusElement.style.display = 'none';

            // Update header filter status
            if (headerFilterStatus) {
                headerFilterStatus.textContent = 'All UUIDs';
            }
        }
    }
}

function clearFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('activationFilter').value = '';
    document.getElementById('searchInput').value = '';

    // 清除搜索高亮
    highlightSearchResults('');

    // 重新应用过滤（实际上是显示所有）
    filterUUIDs();

    // 显示成功通知
    showNotification('Filters cleared', 'info');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: var(--spacing-lg);
        right: var(--spacing-lg);
        background: ${type === 'success' ? 'var(--color-success)' : type === 'error' ? 'var(--color-danger)' : 'var(--color-primary)'};
        color: var(--color-white);
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform var(--transition-base);
        max-width: 400px;
        font-weight: var(--font-weight-medium);
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    return token ? {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    } : {
        'Content-Type': 'application/json'
    };
}

// Real-time Usage Count Update Functions
let usageUpdateInterval;
let lastUsageCounts = new Map();

function startUsageCountUpdates() {
    // 每5秒检查一次使用计数更新
    usageUpdateInterval = setInterval(updateUsageCounts, 5000);
    console.log('🔄 Started real-time usage count updates');
}

function stopUsageCountUpdates() {
    if (usageUpdateInterval) {
        clearInterval(usageUpdateInterval);
        usageUpdateInterval = null;
        console.log('⏹️ Stopped real-time usage count updates');
    }
}

async function updateUsageCounts() {
    try {
        const cards = document.querySelectorAll('.uuid-card-row');
        if (cards.length === 0) return;

        // 收集所有UUID
        const uuids = Array.from(cards).map(card => card.dataset.uuid);

        // 批量获取状态信息
        const promises = uuids.map(uuid =>
            fetch(`/status/${uuid}`, { headers: getAuthHeaders() })
                .then(response => response.ok ? response.json() : null)
                .catch(() => null)
        );

        const results = await Promise.all(promises);
        let updatedCount = 0;

        results.forEach((data, index) => {
            if (!data) return;

            const uuid = uuids[index];
            const card = document.querySelector(`[data-uuid="${uuid}"]`);
            if (!card) return;

            const currentUsageCount = data.usage_count;
            const lastCount = lastUsageCounts.get(uuid) || 0;

            // 如果使用计数发生变化
            if (currentUsageCount !== lastCount) {
                updateUUIDUsageDisplay(card, data);
                lastUsageCounts.set(uuid, currentUsageCount);
                updatedCount++;

                // 如果计数增加，显示通知
                if (currentUsageCount > lastCount) {
                    const increase = currentUsageCount - lastCount;
                    showNotification(
                        `UUID ${uuid.substring(0, 8)}... used ${increase} time${increase > 1 ? 's' : ''} (Total: ${currentUsageCount})`,
                        'info'
                    );
                }
            }
        });

        if (updatedCount > 0) {
            console.log(`📊 Updated ${updatedCount} UUID usage counts`);
        }

    } catch (error) {
        console.error('Error updating usage counts:', error);
    }
}

function updateUUIDUsageDisplay(card, data) {
    // 更新使用计数显示
    const usageCountElement = card.querySelector('.uuid-meta-item:nth-child(3) .uuid-meta-value .status-badge-secondary');
    if (usageCountElement) {
        const oldCount = usageCountElement.textContent;
        usageCountElement.textContent = data.usage_count;

        // 添加更新动画
        usageCountElement.style.transform = 'scale(1.2)';
        usageCountElement.style.background = 'var(--color-primary)';
        usageCountElement.style.color = 'var(--color-white)';

        setTimeout(() => {
            usageCountElement.style.transform = 'scale(1)';
            usageCountElement.style.background = '';
            usageCountElement.style.color = '';
        }, 500);
    }

    // 更新最后使用时间
    if (data.last_used_at) {
        const lastUsedElement = card.querySelector('.uuid-meta-item:nth-child(3) .uuid-meta-value div');
        if (lastUsedElement) {
            const lastUsedDate = new Date(data.last_used_at);
            const formattedTime = lastUsedDate.toLocaleDateString('en-US', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            }).replace(/(\d+)\/(\d+)\/\d+,?\s*/, '$1-$2 ');

            lastUsedElement.textContent = `Last: ${formattedTime}`;

            // 添加更新动画
            lastUsedElement.style.color = 'var(--color-primary)';
            setTimeout(() => {
                lastUsedElement.style.color = '';
            }, 1000);
        }
    }
}

function initializeUsageCounts() {
    // 初始化当前的使用计数
    const cards = document.querySelectorAll('.uuid-card-row');
    cards.forEach(card => {
        const uuid = card.dataset.uuid;
        const usageCountElement = card.querySelector('.uuid-meta-item:nth-child(3) .uuid-meta-value .status-badge-secondary');
        if (usageCountElement) {
            const currentCount = parseInt(usageCountElement.textContent) || 0;
            lastUsageCounts.set(uuid, currentCount);
        }
    });
}

async function manualRefreshUsageCounts() {
    const button = document.getElementById('refreshUsageBtn');
    const icon = button.querySelector('i');
    const originalContent = button.innerHTML;

    // 显示加载状态
    button.disabled = true;
    icon.className = 'fas fa-spinner fa-spin';

    try {
        // 强制更新所有使用计数
        await updateUsageCounts();

        // 显示成功状态
        icon.className = 'fas fa-check';
        button.style.color = 'var(--color-success)';

        showNotification('Usage counts refreshed successfully', 'success');

        // 重置按钮状态
        setTimeout(() => {
            button.disabled = false;
            button.innerHTML = originalContent;
            button.style.color = '';
        }, 1000);

    } catch (error) {
        // 显示错误状态
        icon.className = 'fas fa-exclamation-triangle';
        button.style.color = 'var(--color-danger)';

        showNotification('Failed to refresh usage counts', 'error');

        // 重置按钮状态
        setTimeout(() => {
            button.disabled = false;
            button.innerHTML = originalContent;
            button.style.color = '';
        }, 1000);
    }
}

// Initialize animations on page load
document.addEventListener('DOMContentLoaded', function() {
    // Add staggered animation to UUID cards
    const cards = document.querySelectorAll('.uuid-card-row');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    // Add search highlighting functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            highlightSearchResults(this.value);
            filterUUIDs(); // 同时触发过滤
        });
    }

    // Add filter event listeners
    const statusFilter = document.getElementById('statusFilter');
    const activationFilter = document.getElementById('activationFilter');

    if (statusFilter) {
        statusFilter.addEventListener('change', filterUUIDs);
    }

    if (activationFilter) {
        activationFilter.addEventListener('change', filterUUIDs);
    }

    // Initialize and start real-time usage count updates
    if (cards.length > 0) {
        initializeUsageCounts();
        startUsageCountUpdates();

        // 添加页面可见性检测，当页面不可见时停止更新，可见时恢复
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopUsageCountUpdates();
            } else {
                startUsageCountUpdates();
            }
        });

        // 页面卸载时停止更新
        window.addEventListener('beforeunload', function() {
            stopUsageCountUpdates();
        });
    }
});

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const uuidModal = document.getElementById('uuidModal');
    const testModal = document.getElementById('testModal');

    if (e.target === uuidModal) {
        closeUUIDModal();
    }

    if (e.target === testModal) {
        closeTestModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const uuidModal = document.getElementById('uuidModal');
        const testModal = document.getElementById('testModal');

        if (uuidModal.classList.contains('show')) {
            closeUUIDModal();
        }

        if (testModal.classList.contains('show')) {
            closeTestModal();
        }
    }
});

function highlightSearchResults(searchTerm) {
    const uuidDisplays = document.querySelectorAll('.uuid-display .uuid-text');

    uuidDisplays.forEach(display => {
        const originalText = display.textContent;

        if (searchTerm && originalText.toLowerCase().includes(searchTerm.toLowerCase())) {
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            const highlightedText = originalText.replace(regex, '<span class="search-highlight">$1</span>');
            display.innerHTML = highlightedText;
        } else {
            display.textContent = originalText;
        }
    });
}
</script>
{% endblock %}
