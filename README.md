# UUID Management System

A comprehensive UUID management system built with FastAPI, featuring a web-based admin interface and RESTful API endpoints.

## Features

### Core Functionality
- **UUID Generation**: Automatically generate unique UUIDs
- **One-time Activation**: UUIDs can only be activated once, but validated unlimited times
- **Metadata Tracking**: Store creation date, expiration date, status, usage count, and more
- **Status Management**: Active/inactive status with expiration support
- **Usage Analytics**: Track usage count and last used timestamps

### Admin Interface
- **Role-based Authentication**: Secure admin login with JWT tokens
- **Web Dashboard**: Responsive admin interface with statistics
- **UUID Management**: Create, edit, delete, and monitor UUIDs
- **Real-time Testing**: Test UUID validation directly from the interface
- **Search & Filtering**: Filter UUIDs by status, activation state, and search terms

### API Endpoints

#### Public Endpoints
- `GET /validate/{uuid}` - Validate and potentially activate a UUID
- `GET /status/{uuid}` - Check UUID status (read-only)

#### Admin Endpoints (Authentication Required)
- `POST /admin/login` - Admin authentication
- `GET /admin/stats` - Get UUID statistics
- `GET /admin/uuids` - List all UUIDs with filtering
- `POST /admin/uuids` - Create new UUID
- `GET /admin/uuids/{uuid}` - Get UUID details
- `PUT /admin/uuids/{uuid}` - Update UUID
- `DELETE /admin/uuids/{uuid}` - Delete UUID

## Installation

### Option 1: Docker Deployment (Recommended)

**Prerequisites:**
- Docker Engine 20.10+
- Docker Compose 2.0+

**Quick Start:**
```bash
# Initialize environment
./deploy.sh init

# Start application (development mode)
./deploy.sh start

# Or start with all services (production mode)
./deploy.sh start --production --all-services
```

**Access the application:**
- Web Interface: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health

For detailed Docker deployment instructions, see [DOCKER_DEPLOYMENT.md](DOCKER_DEPLOYMENT.md).

### Option 2: Native Python Installation

**Prerequisites:**
- Python 3.8+
- pip (Python package manager)

**Setup:**

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**:
   Edit the `.env` file to customize settings:
   ```env
   # Database Configuration
   DATABASE_URL=sqlite:///./uuid_management.db

   # Security Configuration
   SECRET_KEY=your-super-secret-key-change-this-in-production
   ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30

   # Admin Configuration
   ADMIN_USERNAME=admin
   ADMIN_PASSWORD=admin123

   # Application Configuration
   APP_NAME=UUID Management System
   APP_VERSION=1.0.0
   DEBUG=True
   ```

4. **Run the application**:
   ```bash
   python -m app.main
   ```

   Or using uvicorn directly:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

5. **Access the application**:
   - Web Interface: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - Alternative API Docs: http://localhost:8000/redoc

## Usage

### Web Interface

1. **Login**: Navigate to http://localhost:8000 and login with:
   - Username: `admin`
   - Password: `admin123`

2. **Dashboard**: View statistics and recent UUIDs

3. **Manage UUIDs**: 
   - Create new UUIDs with optional descriptions and expiration dates
   - Edit existing UUIDs (description, expiration, status)
   - Delete UUIDs
   - Test UUID validation
   - Filter and search UUIDs

### API Usage

#### Validate a UUID (Public)
```bash
curl -X GET "http://localhost:8000/validate/your-uuid-here"
```

Response:
```json
{
  "uuid": "123e4567-e89b-12d3-a456-************",
  "is_valid": true,
  "is_activated": true,
  "is_expired": false,
  "status": "active",
  "message": "UUID validated and activated successfully",
  "activated_now": true
}
```

#### Check UUID Status (Public)
```bash
curl -X GET "http://localhost:8000/status/your-uuid-here"
```

#### Admin Login
```bash
curl -X POST "http://localhost:8000/admin/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123"
```

#### Create UUID (Admin)
```bash
curl -X POST "http://localhost:8000/admin/uuids" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "Test UUID",
    "expires_at": "2024-12-31T23:59:59"
  }'
```

## Database Schema

The system uses a single `uuids` table with the following fields:

- `uuid` (Primary Key): The UUID value
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp
- `expires_at`: Optional expiration date
- `last_used_at`: Last validation timestamp
- `activated_at`: First activation timestamp
- `status`: active/inactive
- `usage_count`: Number of times validated
- `is_activated`: Boolean flag (can only be set once)
- `description`: Optional description
- `created_by`: Creator username

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: Bcrypt password hashing
- **Role-based Access**: Admin-only endpoints
- **Input Validation**: Pydantic schema validation
- **CORS Protection**: Configurable CORS settings

## Production Deployment

### Docker Production Deployment (Recommended)

For production deployment using Docker:

```bash
# 1. Initialize production environment
./deploy.sh init --production

# 2. Edit .env with production settings
nano .env

# 3. Start production services
./deploy.sh start --production --all-services

# 4. Create database backup
./deploy.sh backup
```

**Production checklist:**
- ✅ Change `SECRET_KEY` in `.env`
- ✅ Change admin credentials in `.env`
- ✅ Set `DEBUG=false`
- ✅ Use PostgreSQL instead of SQLite
- ✅ Configure SSL certificates for Nginx
- ✅ Set up monitoring and logging
- ✅ Configure firewall rules

### Native Production Deployment

For native Python production deployment:

1. **Change default credentials** in `.env`
2. **Use PostgreSQL** instead of SQLite:
   ```env
   DATABASE_URL=postgresql://username:password@localhost/uuid_management
   ```
3. **Set strong SECRET_KEY**
4. **Disable DEBUG mode**:
   ```env
   DEBUG=False
   ```
5. **Use HTTPS** and proper reverse proxy (nginx, Apache)
6. **Set up proper logging and monitoring**

## Development

### Project Structure
```
uuid-management-system/
├── app/
│   ├── main.py              # FastAPI application
│   ├── config.py            # Configuration
│   ├── database.py          # Database setup
│   ├── models.py            # SQLAlchemy models
│   ├── schemas.py           # Pydantic schemas
│   ├── crud.py              # Database operations
│   ├── auth.py              # Authentication
│   ├── dependencies.py      # FastAPI dependencies
│   └── routers/
│       ├── admin.py         # Admin endpoints
│       └── public.py        # Public endpoints
├── templates/               # HTML templates
├── static/                  # Static files (CSS, JS)
├── requirements.txt         # Python dependencies
├── .env                     # Environment variables
└── README.md               # This file
```

### Adding Features

To extend the system:

1. **Add new fields** to the UUID model in `models.py`
2. **Update schemas** in `schemas.py`
3. **Add CRUD operations** in `crud.py`
4. **Create new endpoints** in the appropriate router
5. **Update templates** for web interface changes

## License

This project is open source and available under the MIT License.
