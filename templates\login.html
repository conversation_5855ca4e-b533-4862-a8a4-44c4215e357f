<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In - UUID Management System</title>

    <!-- Apple Design System CSS -->
    <link href="/static/css/apple-design-system.css" rel="stylesheet">

    <!-- SF Symbols and Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
        }

        .login-container {
            width: 100%;
            max-width: 400px;
        }

        .login-card {
            background: var(--color-bg-primary);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--color-gray-200);
            overflow: hidden;
            backdrop-filter: blur(20px);
        }

        .login-header {
            text-align: center;
            padding: var(--spacing-3xl) var(--spacing-xl) var(--spacing-xl);
            background: var(--color-bg-primary);
        }

        .login-icon {
            width: 64px;
            height: 64px;
            background: var(--color-primary);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-lg);
            color: var(--color-white);
            font-size: var(--font-size-2xl);
        }

        .login-title {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-text-primary);
            margin: 0 0 var(--spacing-sm) 0;
        }

        .login-subtitle {
            font-size: var(--font-size-base);
            color: var(--color-text-secondary);
            margin: 0;
        }

        .login-form {
            padding: 0 var(--spacing-xl) var(--spacing-xl);
        }

        .login-footer {
            padding: var(--spacing-lg) var(--spacing-xl);
            background: var(--color-bg-secondary);
            text-align: center;
            border-top: 1px solid var(--color-gray-200);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: var(--font-family-system);
            font-size: var(--font-size-base);
            color: var(--color-text-primary);
            background: var(--color-bg-secondary);
            border: 1px solid var(--color-gray-300);
            border-radius: var(--radius-lg);
            transition: all var(--transition-fast);
            min-height: 52px;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--color-primary);
            background: var(--color-bg-primary);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .form-input::placeholder {
            color: var(--color-text-tertiary);
        }

        .btn-login {
            width: 100%;
            background: var(--color-primary);
            color: var(--color-white);
            border: none;
            border-radius: var(--radius-lg);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: var(--font-family-system);
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: all var(--transition-fast);
            min-height: 52px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        .btn-login:hover:not(:disabled) {
            background: var(--color-primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-top: var(--spacing-md);
            font-size: var(--font-size-sm);
        }

        .alert-danger {
            background: rgba(255, 59, 48, 0.1);
            color: var(--color-danger);
            border: 1px solid rgba(255, 59, 48, 0.2);
        }

        .loading-text {
            color: var(--color-text-secondary);
            font-size: var(--font-size-sm);
            text-align: center;
            margin-top: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        .default-credentials {
            font-size: var(--font-size-sm);
            color: var(--color-text-tertiary);
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-icon">
                    <i class="fas fa-key"></i>
                </div>
                <h1 class="login-title">UUID Manager</h1>
                <p class="login-subtitle">Sign in to your account</p>
            </div>

            <div class="login-form">
                <form id="loginForm">
                    <div class="form-group">
                        <input
                            type="text"
                            class="form-input"
                            id="username"
                            name="username"
                            placeholder="Username"
                            required
                            autocomplete="username"
                        >
                    </div>

                    <div class="form-group">
                        <input
                            type="password"
                            class="form-input"
                            id="password"
                            name="password"
                            placeholder="Password"
                            required
                            autocomplete="current-password"
                        >
                    </div>

                    <button type="submit" class="btn-login" id="loginButton">
                        <span id="loginButtonText">
                            <i class="fas fa-arrow-right-to-bracket"></i>
                            Sign In
                        </span>
                        <span id="loginButtonLoading" style="display: none;">
                            <div class="loading-apple"></div>
                            Signing in...
                        </span>
                    </button>
                </form>

                <div id="errorMessage" class="alert alert-danger" style="display: none;"></div>
            </div>

            <div class="login-footer">
                <p class="default-credentials">Default credentials: admin / admin123</p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('errorMessage');
            const loginButton = document.getElementById('loginButton');
            const loginButtonText = document.getElementById('loginButtonText');
            const loginButtonLoading = document.getElementById('loginButtonLoading');

            // Show loading state
            loginButton.disabled = true;
            loginButtonText.style.display = 'none';
            loginButtonLoading.style.display = 'flex';
            errorDiv.style.display = 'none';

            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);

                const response = await fetch('/admin/login', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    // Store token
                    localStorage.setItem('access_token', data.access_token);

                    // Show success state briefly
                    loginButtonLoading.innerHTML = '<i class="fas fa-check"></i> Success!';

                    // Redirect to dashboard after brief delay
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 500);
                } else {
                    throw new Error(data.detail || 'Sign in failed');
                }
            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.style.display = 'block';

                // Reset button state
                loginButton.disabled = false;
                loginButtonText.style.display = 'flex';
                loginButtonLoading.style.display = 'none';

                // Add shake animation to form
                const loginCard = document.querySelector('.login-card');
                loginCard.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    loginCard.style.animation = '';
                }, 500);
            }
        });

        // Add shake animation keyframes
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);

        // Check if already logged in
        if (localStorage.getItem('access_token')) {
            window.location.href = '/dashboard';
        }

        // Add focus states for better accessibility
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                    this.parentElement.style.transition = 'transform 0.2s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
