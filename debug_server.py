#!/usr/bin/env python3
"""
Debug script to identify server startup issues
"""

import sys
import traceback

def test_imports():
    """Test all imports"""
    print("Testing imports...")
    
    try:
        print("  - Importing FastAPI...")
        from fastapi import FastAPI
        print("    ✅ FastAPI imported")
        
        print("  - Importing SQLAlchemy...")
        from sqlalchemy import create_engine
        print("    ✅ SQLAlchemy imported")
        
        print("  - Importing app.config...")
        from app.config import settings
        print(f"    ✅ Config imported - DB URL: {settings.database_url}")
        
        print("  - Importing app.database...")
        from app.database import create_tables, init_db
        print("    ✅ Database module imported")
        
        print("  - Importing app.models...")
        from app.models import AdminUser, UUID
        print("    ✅ Models imported")
        
        print("  - Importing app.auth...")
        from app.auth import authenticate_user, get_password_hash
        print("    ✅ Auth module imported")
        
        print("  - Importing app.crud...")
        from app.crud import initialize_default_admin
        print("    ✅ CRUD module imported")
        
        print("  - Importing app.main...")
        from app.main import app
        print("    ✅ Main app imported")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_database():
    """Test database operations"""
    print("\nTesting database...")
    
    try:
        from app.database import SessionLocal, create_tables, init_db
        from app.crud import initialize_default_admin, get_admin_user_by_username
        from app.auth import get_password_hash
        
        print("  - Creating tables...")
        create_tables()
        print("    ✅ Tables created")
        
        print("  - Initializing database...")
        init_db()
        print("    ✅ Database initialized")
        
        print("  - Testing database connection...")
        db = SessionLocal()
        try:
            # Test if admin user exists
            admin_user = get_admin_user_by_username(db, "admin")
            if admin_user:
                print(f"    ✅ Admin user found: {admin_user.username}")
                print(f"       - ID: {admin_user.id}")
                print(f"       - Is Admin: {admin_user.is_admin}")
                print(f"       - Is Active: {admin_user.is_active}")
                print(f"       - Created: {admin_user.created_at}")
                print(f"       - Failed attempts: {admin_user.failed_login_attempts}")
                print(f"       - Is locked: {admin_user.is_locked}")
            else:
                print("    ❌ Admin user not found")
                
        finally:
            db.close()
            
        return True
        
    except Exception as e:
        print(f"    ❌ Database test failed: {e}")
        traceback.print_exc()
        return False

def test_authentication():
    """Test authentication"""
    print("\nTesting authentication...")
    
    try:
        from app.database import SessionLocal
        from app.auth import authenticate_user, verify_password, get_password_hash
        from app.crud import get_admin_user_by_username
        
        db = SessionLocal()
        try:
            # Test password verification
            admin_user = get_admin_user_by_username(db, "admin")
            if admin_user:
                print(f"  - Admin user hash: {admin_user.hashed_password[:20]}...")
                
                # Test with default password
                test_password = "admin123"
                print(f"  - Testing password: {test_password}")
                
                # Direct password verification
                is_valid = verify_password(test_password, admin_user.hashed_password)
                print(f"    Direct verification: {'✅' if is_valid else '❌'}")
                
                # Full authentication
                auth_user = authenticate_user("admin", test_password, db)
                print(f"    Full authentication: {'✅' if auth_user else '❌'}")
                
            else:
                print("    ❌ No admin user to test")
                
        finally:
            db.close()
            
        return True
        
    except Exception as e:
        print(f"    ❌ Authentication test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🔍 Server Debug Tests")
    print("=" * 50)
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test database
    if not test_database():
        success = False
    
    # Test authentication
    if not test_authentication():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed - server should start normally")
        
        # Try to start server
        print("\n🚀 Starting server...")
        try:
            import uvicorn
            from app.main import app
            uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
        except Exception as e:
            print(f"❌ Server start failed: {e}")
            traceback.print_exc()
    else:
        print("❌ Some tests failed - fix issues before starting server")
        sys.exit(1)

if __name__ == "__main__":
    main()
