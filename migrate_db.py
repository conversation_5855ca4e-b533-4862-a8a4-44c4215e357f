#!/usr/bin/env python3
"""
Database migration script to add new fields for UUID lifecycle management
"""

import sqlite3
import os

def migrate_database():
    """Add new fields to existing database"""
    db_path = "uuid_management.db"
    
    if not os.path.exists(db_path):
        print("Database doesn't exist yet, no migration needed")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if activation_method column exists
        cursor.execute("PRAGMA table_info(uuids)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'activation_method' not in columns:
            print("Adding activation_method column...")
            cursor.execute("ALTER TABLE uuids ADD COLUMN activation_method VARCHAR(20)")
            print("✅ Added activation_method column")
        else:
            print("✅ activation_method column already exists")
        
        conn.commit()
        print("✅ Database migration completed successfully")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
