#!/usr/bin/env python3
"""
Test script to verify the fixes for UUID Management UI issues
"""

import requests

BASE_URL = "http://localhost:8000"

def test_fixes():
    """Test all the fixes applied"""
    print("🔧 Testing UUID Management UI Fixes...")
    
    # Test UUID management page
    response = requests.get(f"{BASE_URL}/manage-uuids")
    print(f"UUID Management page status: {response.status_code}")
    
    if response.status_code != 200:
        print("❌ Failed to load UUID management page")
        return
    
    content = response.text
    
    # Test 1: Check for Apple modal components (fix for Bootstrap modal issue)
    print("\n1. Testing Apple Modal Components...")
    apple_modal_features = [
        'modal-apple',
        'modal-apple-content', 
        'modal-apple-header',
        'modal-apple-body',
        'modal-apple-footer',
        'closeUUIDModal',
        'closeTestModal'
    ]
    
    modal_found = 0
    for feature in apple_modal_features:
        if feature in content:
            print(f"   ✅ {feature}")
            modal_found += 1
        else:
            print(f"   ❌ Missing: {feature}")
    
    print(f"   📊 Apple Modal: {modal_found}/{len(apple_modal_features)} features found")
    
    # Test 2: Check for filter functionality
    print("\n2. Testing Filter Functionality...")
    filter_features = [
        'filterUUIDs',
        'updateFilterCount', 
        'clearFilters',
        'filteredCount',
        'filterStatus',
        'addEventListener(\'change\', filterUUIDs)'
    ]
    
    filter_found = 0
    for feature in filter_features:
        if feature in content:
            print(f"   ✅ {feature}")
            filter_found += 1
        else:
            print(f"   ❌ Missing: {feature}")
    
    print(f"   📊 Filter System: {filter_found}/{len(filter_features)} features found")
    
    # Test 3: Check for UUID count display
    print("\n3. Testing UUID Count Display...")
    count_features = [
        'uuidCount',
        'filteredCount',
        'filterStatus',
        'updateFilterCount'
    ]
    
    count_found = 0
    for feature in count_features:
        if feature in content:
            print(f"   ✅ {feature}")
            count_found += 1
        else:
            print(f"   ❌ Missing: {feature}")
    
    print(f"   📊 Count Display: {count_found}/{len(count_features)} features found")
    
    # Test 4: Check for enhanced notifications
    print("\n4. Testing Enhanced Notifications...")
    notification_features = [
        'showNotification',
        'notification.style.cssText',
        'translateX(100%)',
        'notification.textContent = message'
    ]
    
    notification_found = 0
    for feature in notification_features:
        if feature in content:
            print(f"   ✅ {feature}")
            notification_found += 1
        else:
            print(f"   ❌ Missing: {feature}")
    
    print(f"   📊 Notifications: {notification_found}/{len(notification_features)} features found")
    
    # Test 5: Check that Bootstrap modal classes are removed
    print("\n5. Testing Bootstrap Modal Removal...")
    bootstrap_classes = [
        'class="modal fade"',
        'data-bs-dismiss',
        'btn-close',
        'modal-dialog'
    ]
    
    bootstrap_found = 0
    for feature in bootstrap_classes:
        if feature in content:
            print(f"   ❌ Still present: {feature}")
            bootstrap_found += 1
        else:
            print(f"   ✅ Removed: {feature}")
    
    print(f"   📊 Bootstrap Removal: {len(bootstrap_classes) - bootstrap_found}/{len(bootstrap_classes)} classes removed")
    
    # Summary
    print("\n" + "="*50)
    print("🎯 FIXES SUMMARY:")
    print("="*50)
    
    total_features = len(apple_modal_features) + len(filter_features) + len(count_features) + len(notification_features)
    total_found = modal_found + filter_found + count_found + notification_found
    
    print(f"✅ Apple Modal System: {modal_found}/{len(apple_modal_features)} ({'✓' if modal_found >= 6 else '✗'})")
    print(f"✅ Filter Functionality: {filter_found}/{len(filter_features)} ({'✓' if filter_found >= 4 else '✗'})")
    print(f"✅ Count Display: {count_found}/{len(count_features)} ({'✓' if count_found >= 3 else '✗'})")
    print(f"✅ Notifications: {notification_found}/{len(notification_features)} ({'✓' if notification_found >= 3 else '✗'})")
    print(f"✅ Bootstrap Cleanup: {len(bootstrap_classes) - bootstrap_found}/{len(bootstrap_classes)} ({'✓' if bootstrap_found == 0 else '✗'})")
    
    print(f"\n🎉 Overall: {total_found}/{total_features} features working")
    
    if total_found >= total_features * 0.8:
        print("🎊 EXCELLENT! Most fixes are working correctly.")
    elif total_found >= total_features * 0.6:
        print("👍 GOOD! Most fixes are in place.")
    else:
        print("⚠️  NEEDS ATTENTION! Some fixes may need review.")
    
    print(f"\n🌐 Test the interface at: {BASE_URL}/manage-uuids")
    
    return total_found >= total_features * 0.8

if __name__ == "__main__":
    success = test_fixes()
    exit(0 if success else 1)
