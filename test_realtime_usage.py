#!/usr/bin/env python3
"""
Test script for Real-time Usage Count Updates
Tests the live update functionality for UUID usage counts
"""

import requests
import time
import threading
from datetime import datetime

BASE_URL = "http://localhost:8000"

def authenticate():
    """Authenticate and get access token"""
    data = {"username": "admin", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/admin/login", data=data)
    
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        print("❌ Authentication failed")
        return None

def get_auth_headers(token):
    """Get authentication headers"""
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def create_test_uuid(token):
    """Create a test UUID for usage testing"""
    headers = get_auth_headers(token)
    data = {
        "description": "Real-time Usage Test UUID",
        "expires_at": None
    }
    
    response = requests.post(f"{BASE_URL}/admin/uuids", headers=headers, json=data)
    if response.status_code == 200:
        uuid_data = response.json()
        print(f"✅ Created test UUID: {uuid_data['uuid'][:8]}...")
        return uuid_data['uuid']
    else:
        print("❌ Failed to create test UUID")
        return None

def simulate_uuid_usage(uuid, count=5, interval=3):
    """Simulate UUID usage by calling validation endpoint"""
    print(f"\n🔄 Starting usage simulation for UUID: {uuid[:8]}...")
    print(f"   Will make {count} validation calls every {interval} seconds")
    
    for i in range(count):
        try:
            # Call validation endpoint to increment usage count
            response = requests.get(f"{BASE_URL}/validate/{uuid}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   📊 Usage #{i+1}: Count = {data.get('usage_count', 'unknown')}, Valid = {data.get('is_valid', 'unknown')}")
            else:
                print(f"   ❌ Usage #{i+1}: Failed (Status: {response.status_code})")
            
            if i < count - 1:  # Don't sleep after the last iteration
                time.sleep(interval)
                
        except Exception as e:
            print(f"   ❌ Usage #{i+1}: Error - {e}")
    
    print(f"✅ Completed usage simulation for {uuid[:8]}...")

def test_realtime_updates():
    """Test the real-time usage update functionality"""
    print("🚀 Testing Real-time Usage Count Updates")
    print("="*50)
    
    # Authenticate
    token = authenticate()
    if not token:
        return False
    
    # Create test UUID
    test_uuid = create_test_uuid(token)
    if not test_uuid:
        return False
    
    print(f"\n📋 Test Instructions:")
    print(f"1. Open your browser to: {BASE_URL}/manage-uuids")
    print(f"2. Look for the test UUID: {test_uuid[:8]}...")
    print(f"3. Watch the Usage Count update in real-time")
    print(f"4. Also check the Dashboard: {BASE_URL}/dashboard")
    print(f"5. The usage count should update automatically every 5-10 seconds")
    
    # Wait for user to open the pages
    input(f"\n⏳ Press Enter when you have the pages open and ready...")
    
    # Start usage simulation in a separate thread
    usage_thread = threading.Thread(
        target=simulate_uuid_usage, 
        args=(test_uuid, 8, 4)  # 8 uses, every 4 seconds
    )
    usage_thread.start()
    
    print(f"\n👀 Watch the following in your browser:")
    print(f"   • UUID Management page: Usage Count should update from 0 to 8")
    print(f"   • Dashboard page: Recent UUIDs usage should also update")
    print(f"   • You should see notifications when usage increases")
    print(f"   • The 'Live Updates' indicator should be pulsing green")
    
    # Wait for simulation to complete
    usage_thread.join()
    
    print(f"\n✅ Usage simulation completed!")
    print(f"   The UUID {test_uuid[:8]}... should now show 8 uses")
    print(f"   Check both pages to verify real-time updates worked")
    
    # Final verification
    response = requests.get(f"{BASE_URL}/status/{test_uuid}")
    if response.status_code == 200:
        final_data = response.json()
        print(f"\n📊 Final Status Check:")
        print(f"   UUID: {test_uuid[:8]}...")
        print(f"   Usage Count: {final_data.get('usage_count', 'unknown')}")
        print(f"   Last Used: {final_data.get('last_used_at', 'unknown')}")
        print(f"   Status: {final_data.get('status', 'unknown')}")
        
        if final_data.get('usage_count', 0) >= 8:
            print(f"   ✅ Usage count updated correctly!")
            return True
        else:
            print(f"   ❌ Usage count may not have updated properly")
            return False
    
    return False

def test_ui_features():
    """Test UI features for real-time updates"""
    print(f"\n🎨 Testing UI Features:")
    print(f"="*30)
    
    # Test manage-uuids page
    response = requests.get(f"{BASE_URL}/manage-uuids")
    if response.status_code == 200:
        content = response.text
        
        # Check for real-time update features
        features = [
            'startUsageCountUpdates',
            'updateUsageCounts', 
            'manualRefreshUsageCounts',
            'realTimeStatus',
            'statusIndicator',
            'Live Updates',
            'usage-count-updated'
        ]
        
        found_features = 0
        for feature in features:
            if feature in content:
                print(f"   ✅ {feature}")
                found_features += 1
            else:
                print(f"   ❌ Missing: {feature}")
        
        print(f"   📊 UUID Management: {found_features}/{len(features)} features found")
    
    # Test dashboard page
    response = requests.get(f"{BASE_URL}/dashboard")
    if response.status_code == 200:
        content = response.text
        
        # Check for dashboard real-time features
        dashboard_features = [
            'startDashboardUsageUpdates',
            'updateDashboardUsageCounts',
            'initializeDashboardUsageCounts'
        ]
        
        found_dashboard = 0
        for feature in dashboard_features:
            if feature in content:
                print(f"   ✅ {feature}")
                found_dashboard += 1
            else:
                print(f"   ❌ Missing: {feature}")
        
        print(f"   📊 Dashboard: {found_dashboard}/{len(dashboard_features)} features found")
        
        return found_features >= 5 and found_dashboard >= 2
    
    return False

def main():
    """Run all real-time update tests"""
    print("🔄 Real-time Usage Count Update Tests")
    print("="*50)
    
    try:
        # Test UI features first
        ui_success = test_ui_features()
        
        if ui_success:
            print(f"\n✅ UI features test passed!")
            
            # Run interactive test
            run_interactive = input(f"\n🤔 Run interactive real-time test? (y/n): ").lower().strip()
            
            if run_interactive == 'y':
                test_success = test_realtime_updates()
                
                if test_success:
                    print(f"\n🎉 All real-time update tests passed!")
                    print(f"\n🌟 Features working:")
                    print(f"   ✓ Real-time usage count updates (every 5-10 seconds)")
                    print(f"   ✓ Visual animations when counts change")
                    print(f"   ✓ Notifications for usage increases")
                    print(f"   ✓ Manual refresh button")
                    print(f"   ✓ Live update status indicator")
                    print(f"   ✓ Page visibility optimization")
                    print(f"   ✓ Both UUID Management and Dashboard pages")
                else:
                    print(f"\n⚠️ Some real-time features may need attention")
            else:
                print(f"\n📝 Manual Testing Instructions:")
                print(f"   1. Open: {BASE_URL}/manage-uuids")
                print(f"   2. Open: {BASE_URL}/dashboard")
                print(f"   3. Use UUIDs via validation API")
                print(f"   4. Watch usage counts update automatically")
        else:
            print(f"\n❌ UI features test failed - check implementation")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
