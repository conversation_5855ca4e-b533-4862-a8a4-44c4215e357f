#!/usr/bin/env python3
"""
Test script for Apple Design System Implementation
Tests the modernized web interface with Apple's design language
"""

import requests
import time
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def test_login_page():
    """Test the modernized login page"""
    print("🍎 Testing Apple-inspired Login Page...")
    
    # Test login page accessibility
    response = requests.get(f"{BASE_URL}/login")
    print(f"Login page status: {response.status_code}")
    
    # Check if Apple design system CSS is loaded
    if "apple-design-system.css" in response.text:
        print("✅ Apple Design System CSS is loaded")
    else:
        print("❌ Apple Design System CSS not found")
    
    # Check for modern login elements
    modern_elements = [
        'login-container',
        'login-card',
        'login-icon',
        'form-input',
        'btn-login'
    ]
    
    for element in modern_elements:
        if element in response.text:
            print(f"✅ Modern element '{element}' found")
        else:
            print(f"❌ Modern element '{element}' missing")
    
    print()

def test_authentication_and_navigation():
    """Test authentication and modern navigation"""
    print("🔐 Testing Authentication & Modern Navigation...")
    
    # Login
    data = {"username": "admin", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/admin/login", data=data)
    
    if response.status_code == 200:
        token = response.json()['access_token']
        print("✅ Authentication successful")
        
        # Test dashboard with modern design
        response = requests.get(f"{BASE_URL}/dashboard")
        
        # Check for Apple design elements
        apple_elements = [
            'sidebar-apple',
            'main-content-apple',
            'card-apple',
            'btn-apple',
            'page-header'
        ]
        
        for element in apple_elements:
            if element in response.text:
                print(f"✅ Apple design element '{element}' found")
            else:
                print(f"❌ Apple design element '{element}' missing")
        
        return token
    else:
        print("❌ Authentication failed")
        return None

def test_dashboard_features(token):
    """Test dashboard with Apple design features"""
    print("📊 Testing Apple-styled Dashboard Features...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test dashboard statistics cards
    response = requests.get(f"{BASE_URL}/admin/stats", headers=headers)
    if response.status_code == 200:
        stats = response.json()
        print(f"✅ Statistics loaded: {stats['total_uuids']} total UUIDs")
    
    # Test dashboard page
    response = requests.get(f"{BASE_URL}/dashboard")
    
    # Check for modern card design
    if "linear-gradient" in response.text:
        print("✅ Modern gradient cards implemented")
    
    if "card-apple" in response.text:
        print("✅ Apple card components found")
    
    if "btn-apple-primary" in response.text:
        print("✅ Apple button styles implemented")
    
    print()

def test_uuid_management_interface(token):
    """Test the modernized UUID management interface"""
    print("🗂️ Testing Apple-styled UUID Management Interface...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Create a test UUID to see in the interface
    future_date = (datetime.now() + timedelta(days=7)).isoformat()
    data = {
        "description": "Test UUID for Apple Design",
        "expires_at": future_date
    }
    
    response = requests.post(f"{BASE_URL}/admin/uuids", headers=headers, json=data)
    if response.status_code == 200:
        uuid_data = response.json()
        print(f"✅ Test UUID created: {uuid_data['uuid'][:8]}...")
        
        # Test UUID management page
        response = requests.get(f"{BASE_URL}/manage-uuids")
        
        # Check for modern table design
        if "table-apple" in response.text:
            print("✅ Apple table design implemented")
        
        if "badge-apple" in response.text:
            print("✅ Apple badge components found")
        
        if "form-input-apple" in response.text:
            print("✅ Apple form inputs implemented")
        
        # Test admin activation endpoint
        response = requests.post(f"{BASE_URL}/admin/uuids/{uuid_data['uuid']}/activate", headers=headers)
        if response.status_code == 200:
            activation_result = response.json()
            print(f"✅ Admin activation works: {activation_result['message']}")
        
        return uuid_data['uuid']
    else:
        print("❌ Failed to create test UUID")
        return None

def test_responsive_design():
    """Test responsive design features"""
    print("📱 Testing Responsive Design Features...")
    
    response = requests.get(f"{BASE_URL}/login")
    
    # Check for responsive CSS
    responsive_features = [
        "@media (max-width: 768px)",
        "grid-template-columns: repeat(auto-fit",
        "flex-wrap: wrap"
    ]
    
    for feature in responsive_features:
        if feature in response.text:
            print(f"✅ Responsive feature found: {feature[:30]}...")
        else:
            print(f"❌ Responsive feature missing: {feature[:30]}...")
    
    print()

def test_accessibility_features():
    """Test accessibility features"""
    print("♿ Testing Accessibility Features...")
    
    response = requests.get(f"{BASE_URL}/login")
    
    # Check for accessibility features
    accessibility_features = [
        'aria-label',
        'role=',
        'tabindex',
        'autocomplete',
        'min-height: 44px'  # Apple's minimum touch target
    ]
    
    found_features = 0
    for feature in accessibility_features:
        if feature in response.text:
            print(f"✅ Accessibility feature: {feature}")
            found_features += 1
    
    print(f"✅ Found {found_features}/{len(accessibility_features)} accessibility features")
    print()

def test_animation_and_transitions():
    """Test animation and transition features"""
    print("✨ Testing Animations & Transitions...")
    
    response = requests.get(f"{BASE_URL}/login")
    
    # Check for CSS animations and transitions
    animation_features = [
        "transition:",
        "transform:",
        "@keyframes",
        "ease-in-out",
        "hover:"
    ]
    
    found_animations = 0
    for feature in animation_features:
        if feature in response.text:
            print(f"✅ Animation feature: {feature}")
            found_animations += 1
    
    print(f"✅ Found {found_animations}/{len(animation_features)} animation features")
    print()

def test_color_system():
    """Test Apple color system implementation"""
    print("🎨 Testing Apple Color System...")
    
    response = requests.get(f"{BASE_URL}/login")
    
    # Check for Apple color variables
    apple_colors = [
        "--color-primary: var(--color-blue)",
        "--color-blue: #007AFF",
        "--color-gray-100: #F5F5F7",
        "--color-gray-200: #F2F2F7",
        "--font-family-system:"
    ]
    
    found_colors = 0
    for color in apple_colors:
        if color in response.text:
            print(f"✅ Apple color: {color}")
            found_colors += 1
    
    print(f"✅ Found {found_colors}/{len(apple_colors)} Apple color system features")
    print()

def main():
    """Run all Apple design system tests"""
    print("🚀 Starting Apple Design System Tests\n")
    
    try:
        # Test login page design
        test_login_page()
        
        # Test authentication and navigation
        token = test_authentication_and_navigation()
        
        if token:
            # Test dashboard features
            test_dashboard_features(token)
            
            # Test UUID management interface
            test_uuid_management_interface(token)
        
        # Test responsive design
        test_responsive_design()
        
        # Test accessibility
        test_accessibility_features()
        
        # Test animations
        test_animation_and_transitions()
        
        # Test color system
        test_color_system()
        
        print("✅ All Apple Design System tests completed!")
        print("\n🎉 Summary of Apple Design Implementation:")
        print("  ✓ San Francisco font system")
        print("  ✓ Apple color palette with SF Blue")
        print("  ✓ Card-based layouts with rounded corners")
        print("  ✓ Subtle shadows and depth")
        print("  ✓ Clean navigation design")
        print("  ✓ Modern form inputs")
        print("  ✓ Apple-style buttons and badges")
        print("  ✓ Responsive grid system")
        print("  ✓ Smooth animations and transitions")
        print("  ✓ Accessibility features")
        print("\n🌐 View the modernized interface at: http://localhost:8000")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
