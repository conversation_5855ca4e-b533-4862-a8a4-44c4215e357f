{% extends "base.html" %}

{% block title %}Admin Settings - UUID Manager{% endblock %}

{% block extra_css %}
<style>
/* Additional styles for admin settings */
.settings-section {
    margin-bottom: var(--spacing-2xl);
}

.settings-card {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.password-requirements {
    background: var(--color-gray-50);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.requirement-item.valid {
    color: var(--color-success);
}

.requirement-item.invalid {
    color: var(--color-danger);
}

.password-strength-meter {
    height: 4px;
    background: var(--color-gray-200);
    border-radius: 2px;
    margin-top: var(--spacing-xs);
    overflow: hidden;
}

.password-strength-fill {
    height: 100%;
    transition: width var(--transition-base), background-color var(--transition-base);
    border-radius: 2px;
}

.strength-weak { background: var(--color-danger); }
.strength-fair { background: var(--color-orange); }
.strength-good { background: var(--color-blue); }
.strength-strong { background: var(--color-success); }

.user-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-label {
    font-size: var(--font-size-xs);
    color: var(--color-text-tertiary);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
    font-weight: var(--font-weight-medium);
}

.security-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.security-status.good {
    background: var(--color-success-light);
    color: var(--color-success);
    border: 1px solid var(--color-success);
}

.security-status.warning {
    background: var(--color-orange-light);
    color: var(--color-orange);
    border: 1px solid var(--color-orange);
}

.security-status.danger {
    background: var(--color-danger-light);
    color: var(--color-danger);
    border: 1px solid var(--color-danger);
}
</style>
{% endblock %}

{% block content %}
<!-- Enhanced Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-header-main">
            <h1 class="page-title">
                <i class="fas fa-cog page-title-icon"></i>
                Admin Settings
            </h1>
            <p class="page-subtitle">Manage your admin account settings and security preferences</p>
            <div class="page-meta">
                <div class="page-meta-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>Security Level: <span style="color: var(--color-success);">High</span></span>
                </div>
                <div class="page-meta-separator"></div>
                <div class="page-meta-item">
                    <i class="fas fa-user-cog"></i>
                    <span>Account Type: Administrator</span>
                </div>
            </div>
        </div>
        <div class="page-header-actions">
            <a href="/dashboard" class="btn-apple btn-apple-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
            </a>
        </div>
    </div>
</div>

    <!-- User Information Section -->
    <div class="settings-section">
        <div class="settings-card">
            <h2 style="margin: 0 0 var(--spacing-lg) 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                <i class="fas fa-user" style="color: var(--color-primary);"></i>
                Account Information
            </h2>
            
            <div class="user-info-grid" id="userInfoGrid">
                <!-- User info will be loaded here -->
            </div>
            
            <div id="securityStatus">
                <!-- Security status will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Password Change Section -->
    <div class="settings-section">
        <div class="settings-card">
            <h2 style="margin: 0 0 var(--spacing-lg) 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                <i class="fas fa-key" style="color: var(--color-primary);"></i>
                Change Password
            </h2>
            
            <!-- Password Requirements -->
            <div class="password-requirements">
                <h4 style="margin: 0 0 var(--spacing-md) 0; color: var(--color-text-primary);">
                    <i class="fas fa-shield-alt" style="color: var(--color-primary); margin-right: var(--spacing-xs);"></i>
                    Password Requirements
                </h4>
                <div id="passwordRequirements">
                    <div class="requirement-item" data-requirement="length">
                        <i class="fas fa-times-circle"></i>
                        <span>At least 8 characters long</span>
                    </div>
                    <div class="requirement-item" data-requirement="letters">
                        <i class="fas fa-times-circle"></i>
                        <span>Contains letters (a-z, A-Z)</span>
                    </div>
                    <div class="requirement-item" data-requirement="numbers">
                        <i class="fas fa-times-circle"></i>
                        <span>Contains numbers (0-9)</span>
                    </div>
                    <div class="requirement-item" data-requirement="special">
                        <i class="fas fa-times-circle"></i>
                        <span>Contains special characters (!@#$%^&*()_+-=[]{}|;:,.<>?)</span>
                    </div>
                    <div class="requirement-item" data-requirement="different">
                        <i class="fas fa-times-circle"></i>
                        <span>Different from current password</span>
                    </div>
                </div>
            </div>
            
            <!-- Password Change Form -->
            <form id="passwordChangeForm">
                <div class="form-group-apple">
                    <label for="currentPassword" class="form-label-apple">Current Password</label>
                    <input
                        type="password"
                        class="form-input-apple"
                        id="currentPassword"
                        name="current_password"
                        required
                        placeholder="Enter your current password"
                    >
                </div>
                
                <div class="form-group-apple">
                    <label for="newPassword" class="form-label-apple">New Password</label>
                    <input
                        type="password"
                        class="form-input-apple"
                        id="newPassword"
                        name="new_password"
                        required
                        placeholder="Enter your new password"
                    >
                    <div class="password-strength-meter">
                        <div class="password-strength-fill" id="strengthFill"></div>
                    </div>
                    <div id="strengthText" style="font-size: var(--font-size-xs); color: var(--color-text-secondary); margin-top: var(--spacing-xs);"></div>
                </div>
                
                <div class="form-group-apple">
                    <label for="confirmPassword" class="form-label-apple">Confirm New Password</label>
                    <input
                        type="password"
                        class="form-input-apple"
                        id="confirmPassword"
                        name="confirm_password"
                        required
                        placeholder="Confirm your new password"
                    >
                    <div id="passwordMatch" style="font-size: var(--font-size-xs); margin-top: var(--spacing-xs);"></div>
                </div>
                
                <div style="display: flex; gap: var(--spacing-md); margin-top: var(--spacing-xl);">
                    <button type="submit" class="btn-apple btn-apple-primary" id="changePasswordBtn">
                        <i class="fas fa-key"></i>
                        Change Password
                    </button>
                    <button type="button" class="btn-apple btn-apple-secondary" onclick="resetPasswordForm()">
                        <i class="fas fa-undo"></i>
                        Reset Form
                    </button>
                </div>
            </form>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
// Admin Settings JavaScript
let userSettings = null;

// Load admin settings on page load
document.addEventListener('DOMContentLoaded', function() {
    loadAdminSettings();
    setupPasswordValidation();
});

async function loadAdminSettings() {
    try {
        const response = await fetch('/admin/settings', {
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            userSettings = await response.json();
            displayUserInfo(userSettings.user);
            displaySecurityStatus(userSettings.user);
        } else {
            showNotification('Failed to load admin settings', 'error');
        }
    } catch (error) {
        showNotification(`Error loading settings: ${error.message}`, 'error');
    }
}

function displayUserInfo(user) {
    const userInfoGrid = document.getElementById('userInfoGrid');
    
    const formatDate = (dateStr) => {
        if (!dateStr) return 'Never';
        return new Date(dateStr).toLocaleString();
    };
    
    userInfoGrid.innerHTML = `
        <div class="info-item">
            <div class="info-label">Username</div>
            <div class="info-value">${user.username}</div>
        </div>
        <div class="info-item">
            <div class="info-label">Account Created</div>
            <div class="info-value">${formatDate(user.created_at)}</div>
        </div>
        <div class="info-item">
            <div class="info-label">Last Login</div>
            <div class="info-value">${formatDate(user.last_login_at)}</div>
        </div>
        <div class="info-item">
            <div class="info-label">Password Last Changed</div>
            <div class="info-value">${formatDate(user.password_changed_at)}</div>
        </div>
        <div class="info-item">
            <div class="info-label">Failed Login Attempts</div>
            <div class="info-value">${user.failed_login_attempts}</div>
        </div>
        <div class="info-item">
            <div class="info-label">Account Status</div>
            <div class="info-value">
                <span class="badge-apple badge-apple-${user.is_active ? 'success' : 'danger'}">
                    ${user.is_active ? 'Active' : 'Inactive'}
                </span>
            </div>
        </div>
    `;
}

function displaySecurityStatus(user) {
    const securityStatus = document.getElementById('securityStatus');
    
    let statusClass = 'good';
    let statusIcon = 'fas fa-shield-alt';
    let statusText = 'Account security is good';
    
    if (user.is_locked) {
        statusClass = 'danger';
        statusIcon = 'fas fa-lock';
        statusText = 'Account is locked due to failed login attempts';
    } else if (user.failed_login_attempts > 0) {
        statusClass = 'warning';
        statusIcon = 'fas fa-exclamation-triangle';
        statusText = `${user.failed_login_attempts} failed login attempt${user.failed_login_attempts > 1 ? 's' : ''} recorded`;
    } else if (!user.password_changed_at) {
        statusClass = 'warning';
        statusIcon = 'fas fa-key';
        statusText = 'Consider changing your password from the default';
    }
    
    securityStatus.innerHTML = `
        <div class="security-status ${statusClass}">
            <i class="${statusIcon}"></i>
            <span>${statusText}</span>
        </div>
    `;
}

// Password validation and strength checking
function setupPasswordValidation() {
    const newPasswordInput = document.getElementById('newPassword');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const currentPasswordInput = document.getElementById('currentPassword');
    
    newPasswordInput.addEventListener('input', function() {
        validatePasswordStrength(this.value);
        checkPasswordMatch();
    });
    
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);
    currentPasswordInput.addEventListener('input', validatePasswordRequirements);
    
    // Form submission
    document.getElementById('passwordChangeForm').addEventListener('submit', handlePasswordChange);
}

function validatePasswordStrength(password) {
    const requirements = {
        length: password.length >= 8,
        letters: /[a-zA-Z]/.test(password),
        numbers: /\d/.test(password),
        special: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password),
        different: password !== document.getElementById('currentPassword').value
    };
    
    // Update requirement indicators
    Object.keys(requirements).forEach(req => {
        const element = document.querySelector(`[data-requirement="${req}"]`);
        const icon = element.querySelector('i');
        
        if (requirements[req]) {
            element.classList.add('valid');
            element.classList.remove('invalid');
            icon.className = 'fas fa-check-circle';
        } else {
            element.classList.add('invalid');
            element.classList.remove('valid');
            icon.className = 'fas fa-times-circle';
        }
    });
    
    // Calculate strength
    const validCount = Object.values(requirements).filter(Boolean).length;
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');
    
    let strength = 'weak';
    let width = '25%';
    let text = 'Weak';
    
    if (validCount >= 5) {
        strength = 'strong';
        width = '100%';
        text = 'Strong';
    } else if (validCount >= 4) {
        strength = 'good';
        width = '75%';
        text = 'Good';
    } else if (validCount >= 3) {
        strength = 'fair';
        width = '50%';
        text = 'Fair';
    }
    
    strengthFill.className = `password-strength-fill strength-${strength}`;
    strengthFill.style.width = width;
    strengthText.textContent = `Password strength: ${text}`;
    strengthText.style.color = getComputedStyle(strengthFill).backgroundColor;
}

function checkPasswordMatch() {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const matchElement = document.getElementById('passwordMatch');
    
    if (confirmPassword === '') {
        matchElement.textContent = '';
        return;
    }
    
    if (newPassword === confirmPassword) {
        matchElement.textContent = '✓ Passwords match';
        matchElement.style.color = 'var(--color-success)';
    } else {
        matchElement.textContent = '✗ Passwords do not match';
        matchElement.style.color = 'var(--color-danger)';
    }
}

function validatePasswordRequirements() {
    // Trigger validation when current password changes
    const newPassword = document.getElementById('newPassword').value;
    if (newPassword) {
        validatePasswordStrength(newPassword);
    }
}

async function handlePasswordChange(event) {
    event.preventDefault();
    
    const button = document.getElementById('changePasswordBtn');
    const originalContent = button.innerHTML;
    
    // Show loading state
    button.disabled = true;
    button.innerHTML = '<div class="loading-apple"></div> Changing Password...';
    
    try {
        const formData = new FormData(event.target);
        const data = {
            current_password: formData.get('current_password'),
            new_password: formData.get('new_password'),
            confirm_password: formData.get('confirm_password')
        };
        
        const response = await fetch('/admin/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeaders()
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            // Show success
            button.innerHTML = '<i class="fas fa-check"></i> Password Changed!';
            button.style.background = 'var(--color-success)';
            
            showNotification('Password changed successfully!', 'success');
            
            // Reset form after delay
            setTimeout(() => {
                resetPasswordForm();
                button.disabled = false;
                button.innerHTML = originalContent;
                button.style.background = '';
                
                // Reload settings to update last changed date
                loadAdminSettings();
            }, 2000);
            
        } else {
            throw new Error(result.detail || 'Failed to change password');
        }
        
    } catch (error) {
        showNotification(`Error: ${error.message}`, 'error');
        
        // Reset button
        button.disabled = false;
        button.innerHTML = originalContent;
    }
}

function resetPasswordForm() {
    document.getElementById('passwordChangeForm').reset();
    
    // Reset requirement indicators
    document.querySelectorAll('.requirement-item').forEach(item => {
        item.classList.remove('valid');
        item.classList.add('invalid');
        item.querySelector('i').className = 'fas fa-times-circle';
    });
    
    // Reset strength meter
    const strengthFill = document.getElementById('strengthFill');
    strengthFill.style.width = '0%';
    strengthFill.className = 'password-strength-fill';
    
    document.getElementById('strengthText').textContent = '';
    document.getElementById('passwordMatch').textContent = '';
}

function getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: var(--spacing-lg);
        right: var(--spacing-lg);
        background: ${type === 'success' ? 'var(--color-success)' : type === 'error' ? 'var(--color-danger)' : 'var(--color-primary)'};
        color: var(--color-white);
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform var(--transition-base);
        max-width: 400px;
        font-weight: var(--font-weight-medium);
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
}
</script>
{% endblock %}
