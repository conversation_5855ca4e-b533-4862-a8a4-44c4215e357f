#!/bin/bash

# Multi-architecture Docker build script for UUID Management System
# Supports both amd64 and arm64 architectures

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="uuid-management"
IMAGE_TAG="latest"
REGISTRY=""
PLATFORMS="linux/amd64,linux/arm64"
DOCKERFILE="Dockerfile"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} ✅ $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} ⚠️  $1"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} ❌ $1"
}

# Help function
show_help() {
    cat << EOF
UUID Management System - Multi-Architecture Docker Build Script

Usage: $0 [OPTIONS]

OPTIONS:
    -h, --help              Show this help message
    -n, --name NAME         Image name (default: uuid-management)
    -t, --tag TAG           Image tag (default: latest)
    -r, --registry REGISTRY Registry prefix (e.g., docker.io/username)
    -p, --platforms PLATFORMS Target platforms (default: linux/amd64,linux/arm64)
    -f, --file DOCKERFILE   Dockerfile path (default: Dockerfile)
    --push                  Push to registry after build
    --no-cache              Build without cache
    --load                  Load image to local Docker (single platform only)
    --dev                   Development build (current platform only)

EXAMPLES:
    # Build for current platform only (development)
    $0 --dev

    # Build for multiple platforms
    $0 --name myapp --tag v1.0.0

    # Build and push to registry
    $0 --registry docker.io/username --push

    # Build with no cache
    $0 --no-cache

    # Build for specific platforms
    $0 --platforms linux/amd64

EOF
}

# Parse command line arguments
PUSH=false
NO_CACHE=false
LOAD=false
DEV_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -p|--platforms)
            PLATFORMS="$2"
            shift 2
            ;;
        -f|--file)
            DOCKERFILE="$2"
            shift 2
            ;;
        --push)
            PUSH=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --load)
            LOAD=true
            shift
            ;;
        --dev)
            DEV_MODE=true
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Construct full image name
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
else
    FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
fi

# Development mode adjustments
if [ "$DEV_MODE" = true ]; then
    PLATFORMS=$(docker version --format '{{.Server.Os}}/{{.Server.Arch}}')
    LOAD=true
    log_warning "Development mode: building for current platform only (${PLATFORMS})"
fi

# Validate Docker and buildx
check_requirements() {
    log "Checking requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check buildx
    if ! docker buildx version &> /dev/null; then
        log_error "Docker buildx is not available"
        exit 1
    fi
    
    # Check Dockerfile
    if [ ! -f "$DOCKERFILE" ]; then
        log_error "Dockerfile not found: $DOCKERFILE"
        exit 1
    fi
    
    log_success "Requirements check passed"
}

# Setup buildx builder
setup_builder() {
    log "Setting up Docker buildx builder..."
    
    # Create builder if it doesn't exist
    if ! docker buildx inspect uuid-builder &> /dev/null; then
        log "Creating new buildx builder..."
        docker buildx create --name uuid-builder --driver docker-container --bootstrap
    fi
    
    # Use the builder
    docker buildx use uuid-builder
    
    log_success "Builder setup completed"
}

# Build the image
build_image() {
    log "Building Docker image..."
    log "Image: $FULL_IMAGE_NAME"
    log "Platforms: $PLATFORMS"
    log "Dockerfile: $DOCKERFILE"
    
    # Construct build command
    BUILD_CMD="docker buildx build"
    BUILD_CMD="$BUILD_CMD --platform $PLATFORMS"
    BUILD_CMD="$BUILD_CMD -t $FULL_IMAGE_NAME"
    BUILD_CMD="$BUILD_CMD -f $DOCKERFILE"
    
    # Add optional flags
    if [ "$NO_CACHE" = true ]; then
        BUILD_CMD="$BUILD_CMD --no-cache"
        log "Building without cache"
    fi
    
    if [ "$PUSH" = true ]; then
        BUILD_CMD="$BUILD_CMD --push"
        log "Will push to registry after build"
    elif [ "$LOAD" = true ]; then
        BUILD_CMD="$BUILD_CMD --load"
        log "Will load to local Docker after build"
    fi
    
    # Add build context
    BUILD_CMD="$BUILD_CMD ."
    
    log "Executing: $BUILD_CMD"
    
    # Execute build
    if eval $BUILD_CMD; then
        log_success "Build completed successfully"
    else
        log_error "Build failed"
        exit 1
    fi
}

# Verify the build
verify_build() {
    if [ "$LOAD" = true ] || [ "$DEV_MODE" = true ]; then
        log "Verifying local image..."
        
        if docker images | grep -q "$IMAGE_NAME.*$IMAGE_TAG"; then
            log_success "Image available locally: $FULL_IMAGE_NAME"
            
            # Show image details
            docker images | grep "$IMAGE_NAME.*$IMAGE_TAG"
        else
            log_warning "Image not found locally (this is normal for multi-platform builds without --load)"
        fi
    fi
    
    if [ "$PUSH" = true ]; then
        log_success "Image pushed to registry: $FULL_IMAGE_NAME"
    fi
}

# Main execution
main() {
    log "🚀 Starting multi-architecture Docker build for UUID Management System"
    log "=================================================="
    
    check_requirements
    
    if [ "$DEV_MODE" = false ]; then
        setup_builder
    fi
    
    build_image
    verify_build
    
    log "=================================================="
    log_success "🎉 Build process completed successfully!"
    
    if [ "$LOAD" = true ] || [ "$DEV_MODE" = true ]; then
        log ""
        log "You can now run the container with:"
        log "  docker run -p 8000:8000 $FULL_IMAGE_NAME"
        log ""
        log "Or use docker-compose:"
        log "  docker-compose up -d"
    fi
    
    if [ "$PUSH" = true ]; then
        log ""
        log "Image has been pushed to registry and is available for deployment on:"
        log "  - linux/amd64 (Intel/AMD x86_64)"
        log "  - linux/arm64 (ARM64/Apple Silicon)"
    fi
}

# Run main function
main "$@"
