"""
Background tasks for UUID lifecycle management
"""

import asyncio
import logging
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from .database import SessionLocal
from .crud import update_expired_uuids

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class UUIDLifecycleManager:
    """Manages UUID lifecycle background tasks"""
    
    def __init__(self, check_interval_minutes: int = 5):
        self.check_interval_minutes = check_interval_minutes
        self.running = False
        self.task = None
    
    async def start(self):
        """Start the background task"""
        if self.running:
            logger.warning("UUID lifecycle manager is already running")
            return
        
        self.running = True
        self.task = asyncio.create_task(self._run_periodic_checks())
        logger.info(f"Started UUID lifecycle manager with {self.check_interval_minutes} minute intervals")
    
    async def stop(self):
        """Stop the background task"""
        if not self.running:
            return
        
        self.running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        
        logger.info("Stopped UUID lifecycle manager")
    
    async def _run_periodic_checks(self):
        """Run periodic checks for UUID expiration"""
        while self.running:
            try:
                await self._check_and_update_expired_uuids()
                await asyncio.sleep(self.check_interval_minutes * 60)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in UUID lifecycle check: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _check_and_update_expired_uuids(self):
        """Check and update expired UUIDs"""
        db = SessionLocal()
        try:
            updated_count = update_expired_uuids(db)
            if updated_count > 0:
                logger.info(f"Updated {updated_count} expired UUIDs to 'expired' status")
        except Exception as e:
            logger.error(f"Error updating expired UUIDs: {e}")
        finally:
            db.close()


# Global instance
lifecycle_manager = UUIDLifecycleManager()


async def start_background_tasks():
    """Start all background tasks"""
    await lifecycle_manager.start()


async def stop_background_tasks():
    """Stop all background tasks"""
    await lifecycle_manager.stop()


def force_update_expired_uuids():
    """Force an immediate update of expired UUIDs (synchronous)"""
    db = SessionLocal()
    try:
        updated_count = update_expired_uuids(db)
        logger.info(f"Force updated {updated_count} expired UUIDs")
        return updated_count
    except Exception as e:
        logger.error(f"Error in force update: {e}")
        return 0
    finally:
        db.close()
