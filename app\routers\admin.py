from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Form
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import List, Optional
from ..database import get_db
from ..auth import authenticate_user, create_access_token
from ..dependencies import get_current_admin_user
from ..crud import (
    create_uuid, get_uuids, get_uuid, update_uuid, delete_uuid,
    get_uuid_stats, search_uuids, admin_activate_uuid, get_uuids_with_expiration_check
)
from ..schemas import (
    Token, UUIDCreate, UUIDResponse, UUIDUpdate, UUIDListResponse,
    UUIDStatsResponse, User, LoginRequest, UUIDActivationResponse,
    PasswordChangeRequest, PasswordChangeResponse, AdminUserResponse, AdminSettingsResponse
)
from ..config import settings

router = APIRouter(
    prefix="/admin",
    tags=["admin"]
)


@router.post("/login", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """Admin login endpoint"""
    user = authenticate_user(form_data.username, form_data.password, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}


@router.post("/login-form", response_model=Token)
async def login_form(
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    """Admin login endpoint for form submission"""
    user = authenticate_user(username, password, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}


@router.get("/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_admin_user)):
    """Get current admin user info"""
    return current_user


@router.get("/stats", response_model=UUIDStatsResponse)
async def get_stats(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get UUID statistics"""
    stats = get_uuid_stats(db)
    return UUIDStatsResponse(**stats)


@router.get("/uuids", response_model=UUIDListResponse)
async def list_uuids(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    is_activated: Optional[bool] = None,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """List all UUIDs with optional filtering"""
    if status or is_activated is not None:
        uuids = search_uuids(db, status=status, is_activated=is_activated, skip=skip, limit=limit)
    else:
        uuids = get_uuids_with_expiration_check(db, skip=skip, limit=limit)
    
    total = len(uuids)  # In a real app, you'd want a separate count query
    
    return UUIDListResponse(
        total=total,
        uuids=[UUIDResponse.model_validate(uuid) for uuid in uuids]
    )


@router.post("/uuids", response_model=UUIDResponse)
async def create_new_uuid(
    uuid_data: UUIDCreate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Create a new UUID"""
    db_uuid = create_uuid(db, uuid_data, created_by=current_user.username)
    return UUIDResponse.model_validate(db_uuid)


@router.get("/uuids/{uuid}", response_model=UUIDResponse)
async def get_uuid_details(
    uuid: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get details of a specific UUID"""
    db_uuid = get_uuid(db, uuid)
    if not db_uuid:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="UUID not found"
        )
    return UUIDResponse.model_validate(db_uuid)


@router.put("/uuids/{uuid}", response_model=UUIDResponse)
async def update_uuid_details(
    uuid: str,
    uuid_update: UUIDUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Update a UUID"""
    db_uuid = update_uuid(db, uuid, uuid_update)
    if not db_uuid:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="UUID not found"
        )
    return UUIDResponse.model_validate(db_uuid)


@router.delete("/uuids/{uuid}")
async def delete_uuid_endpoint(
    uuid: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Delete a UUID"""
    success = delete_uuid(db, uuid)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="UUID not found"
        )
    return {"message": "UUID deleted successfully"}


@router.post("/uuids/{uuid}/activate", response_model=UUIDActivationResponse)
async def activate_uuid_endpoint(
    uuid: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Manually activate a UUID via admin interface"""
    db_uuid, was_activated, message = admin_activate_uuid(db, uuid)

    if not db_uuid:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="UUID not found"
        )

    return UUIDActivationResponse(
        uuid=db_uuid.uuid,
        success=was_activated,
        message=message,
        was_already_activated=db_uuid.is_activated and not was_activated,
        activation_method=db_uuid.activation_method,
        activated_at=db_uuid.activated_at
    )


# ===== ADMIN SETTINGS AND PASSWORD MANAGEMENT =====

@router.get("/settings", response_model=AdminSettingsResponse)
async def get_admin_settings(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get admin settings and user information"""
    from ..crud import get_admin_user_by_username

    # Get full user information from database
    db_user = get_admin_user_by_username(db, current_user.username)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin user not found"
        )

    user_response = AdminUserResponse(
        id=db_user.id,
        username=db_user.username,
        is_admin=db_user.is_admin,
        is_active=db_user.is_active,
        created_at=db_user.created_at,
        updated_at=db_user.updated_at,
        last_login_at=db_user.last_login_at,
        password_changed_at=db_user.password_changed_at,
        failed_login_attempts=db_user.failed_login_attempts,
        is_locked=db_user.is_locked
    )

    return AdminSettingsResponse(user=user_response)


@router.post("/change-password", response_model=PasswordChangeResponse)
async def change_password(
    password_request: PasswordChangeRequest,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Change admin password"""
    from ..crud import get_admin_user_by_username, change_admin_password

    # Get current user from database
    db_user = get_admin_user_by_username(db, current_user.username)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin user not found"
        )

    # Validate password confirmation
    if not password_request.validate_passwords_match():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="New password and confirmation do not match"
        )

    # Validate password strength
    is_strong, errors = password_request.validate_password_strength()
    if not is_strong:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Password does not meet requirements: {'; '.join(errors)}"
        )

    # Change password
    success, message, updated_user = change_admin_password(
        db=db,
        user_id=db_user.id,
        current_password=password_request.current_password,
        new_password=password_request.new_password
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message
        )

    return PasswordChangeResponse(
        success=True,
        message=message,
        changed_at=updated_user.password_changed_at if updated_user else None
    )
