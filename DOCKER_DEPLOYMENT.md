# Docker Deployment Guide

This guide provides comprehensive instructions for deploying the UUID Management System using Docker with support for both amd64 and arm64 architectures.

## 🚀 Quick Start

### Prerequisites

- Docker Engine 20.10+ with BuildKit support
- Docker Compose 2.0+
- At least 512MB RAM available
- 1GB disk space for images and data

### Basic Deployment (SQLite)

1. **<PERSON>lone and prepare the environment:**
   ```bash
   git clone <repository-url>
   cd uuid-management-system
   cp .env.example .env
   ```

2. **Customize environment variables:**
   ```bash
   # Edit .env file with your preferred settings
   nano .env
   ```

3. **Build and start the application:**
   ```bash
   docker-compose up -d
   ```

4. **Access the application:**
   - Web Interface: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

## 🏗️ Architecture Overview

### Multi-Architecture Support

The Docker setup supports both `linux/amd64` and `linux/arm64` platforms:

- **amd64**: Intel/AMD x86_64 processors
- **arm64**: ARM64 processors (Apple Silicon, ARM servers)

### Container Structure

```
┌─────────────────────────────────────────┐
│              Nginx (Optional)           │
│         Reverse Proxy & SSL             │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│         UUID Management App             │
│        FastAPI + Uvicorn               │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│            Database                     │
│      SQLite (dev) / PostgreSQL (prod)  │
└─────────────────────────────────────────┘
```

## 📋 Deployment Options

### Option 1: Development (SQLite)

**Best for:** Local development, testing, small deployments

```bash
# Start with SQLite database
docker-compose up -d uuid-management
```

**Features:**
- Single container deployment
- SQLite database with persistent volume
- Suitable for < 100 concurrent users
- Easy backup and migration

### Option 2: Production (PostgreSQL + Nginx)

**Best for:** Production deployments, high availability

```bash
# Copy and customize production environment
cp .env.example .env.prod

# Edit production settings
nano .env.prod

# Start with PostgreSQL and Nginx
docker-compose --env-file .env.prod -f docker-compose.yml -f docker-compose.prod.yml up -d
```

**Features:**
- PostgreSQL database for better performance
- Nginx reverse proxy with rate limiting
- SSL/TLS support (requires certificate configuration)
- Resource limits and health checks
- Suitable for production workloads

### Option 3: Custom Profiles

Enable specific services using Docker Compose profiles:

```bash
# With PostgreSQL only
docker-compose --profile postgres up -d

# With Redis caching
docker-compose --profile redis up -d

# With Nginx reverse proxy
docker-compose --profile nginx up -d

# All services
docker-compose --profile postgres --profile redis --profile nginx up -d
```

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DATABASE_URL` | `sqlite:///./data/uuid_management.db` | Database connection string |
| `SECRET_KEY` | `your-super-secret-key...` | JWT secret key (CHANGE IN PRODUCTION!) |
| `ADMIN_USERNAME` | `admin` | Default admin username |
| `ADMIN_PASSWORD` | `admin123!` | Default admin password (CHANGE IN PRODUCTION!) |
| `DEBUG` | `true` | Debug mode (set to `false` in production) |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | `30` | JWT token expiration time |

### Database Configuration

#### SQLite (Default)
```env
DATABASE_URL=sqlite:///./data/uuid_management.db
```

#### PostgreSQL (Recommended for Production)
```env
DATABASE_URL=*********************************************/uuid_management
POSTGRES_USER=uuid_user
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=uuid_management
```

### Security Configuration

**⚠️ IMPORTANT: Change these values in production!**

```env
# Generate with: openssl rand -hex 32
SECRET_KEY=your-generated-secret-key

# Strong admin credentials
ADMIN_USERNAME=your_admin_username
ADMIN_PASSWORD=YourVerySecurePassword123!

# Disable debug mode
DEBUG=false
```

## 🔨 Building and Customization

### Building Multi-Architecture Images

```bash
# Enable Docker BuildKit
export DOCKER_BUILDKIT=1

# Build for current platform
docker build -t uuid-management:latest .

# Build for multiple platforms
docker buildx create --use
docker buildx build --platform linux/amd64,linux/arm64 -t uuid-management:latest .
```

### Custom Build Arguments

```bash
# Build with specific Python version
docker build --build-arg PYTHON_VERSION=3.11 -t uuid-management:custom .
```

## 📊 Monitoring and Maintenance

### Health Checks

The application includes built-in health checks:

```bash
# Check application health
curl http://localhost:8000/health

# Check container health
docker-compose ps
```

### Logs

```bash
# View application logs
docker-compose logs -f uuid-management

# View all service logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f postgres
```

### Database Backup

#### SQLite Backup
```bash
# Create backup
docker-compose exec uuid-management cp /app/data/uuid_management.db /app/data/backup_$(date +%Y%m%d_%H%M%S).db

# Copy backup to host
docker cp uuid-management-app:/app/data/backup_20240101_120000.db ./backup.db
```

#### PostgreSQL Backup
```bash
# Create database dump
docker-compose exec postgres pg_dump -U uuid_user uuid_management > backup.sql

# Restore from backup
docker-compose exec -T postgres psql -U uuid_user uuid_management < backup.sql
```

## 🔄 Updates and Maintenance

### Updating the Application

```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose build --no-cache
docker-compose up -d
```

### Database Migrations

Database migrations are handled automatically by the entrypoint script. For manual migration:

```bash
# Run migrations manually
docker-compose exec uuid-management python migrate_db.py
```

## 🛡️ Security Best Practices

### Production Security Checklist

- [ ] Change default `SECRET_KEY`
- [ ] Change default admin credentials
- [ ] Set `DEBUG=false`
- [ ] Use PostgreSQL instead of SQLite
- [ ] Enable SSL/TLS in Nginx
- [ ] Configure firewall rules
- [ ] Set up log monitoring
- [ ] Regular security updates
- [ ] Backup strategy implementation

### Network Security

```bash
# Create custom network with restricted access
docker network create --driver bridge uuid_secure_network

# Run with custom network
docker-compose -f docker-compose.yml -f docker-compose.secure.yml up -d
```

## 🚨 Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Change port in docker-compose.yml
   ports:
     - "8080:8000"  # Use port 8080 instead
   ```

2. **Permission denied errors:**
   ```bash
   # Fix file permissions
   chmod +x docker-entrypoint.sh
   ```

3. **Database connection issues:**
   ```bash
   # Check database logs
   docker-compose logs postgres
   
   # Verify database is ready
   docker-compose exec postgres pg_isready -U uuid_user
   ```

4. **Memory issues:**
   ```bash
   # Increase Docker memory limit or add swap
   # Check container resource usage
   docker stats
   ```

### Debug Mode

```bash
# Run with debug output
docker-compose up --no-daemon

# Access container shell
docker-compose exec uuid-management bash

# Check application status
docker-compose exec uuid-management python -c "from app.main import app; print('App loaded successfully')"
```

## 📈 Performance Tuning

### Resource Limits

Adjust resource limits in `docker-compose.prod.yml`:

```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 1G
    reservations:
      cpus: '0.5'
      memory: 256M
```

### Database Optimization

For PostgreSQL, tune configuration in `docker-compose.prod.yml`:

```yaml
command: >
  postgres
  -c max_connections=200
  -c shared_buffers=256MB
  -c effective_cache_size=512MB
```

## 📞 Support

For issues and questions:

1. Check the logs: `docker-compose logs`
2. Verify health checks: `curl http://localhost:8000/health`
3. Review this documentation
4. Check the main README.md for API documentation
