from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from .database import get_db
from .auth import get_current_user
from .schemas import User

# Security scheme
security = HTTPBearer()


def get_current_admin_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """Dependency to get current admin user"""
    token = credentials.credentials
    user = get_current_user(token)
    if not user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return user


def get_db_session() -> Session:
    """Dependency to get database session"""
    return Depends(get_db)
