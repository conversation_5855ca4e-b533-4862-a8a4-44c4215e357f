version: '3.8'

services:
  # UUID Management System Application
  uuid-management:
    build:
      context: .
      dockerfile: Dockerfile
      platforms:
        - linux/amd64
        - linux/arm64
    container_name: uuid-management-app
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # Database Configuration
      DATABASE_URL: sqlite:///./data/uuid_management.db
      
      # Security Configuration (CHANGE THESE IN PRODUCTION!)
      SECRET_KEY: your-super-secret-key-change-this-in-production
      ALGORITHM: HS256
      ACCESS_TOKEN_EXPIRE_MINUTES: 30
      
      # Admin Configuration (CHANGE THESE IN PRODUCTION!)
      ADMIN_USERNAME: admin
      ADMIN_PASSWORD: admin123!
      
      # Application Configuration
      APP_NAME: UUID Management System
      APP_VERSION: 1.0.0
      DEBUG: "true"
      
      # Python Configuration
      PYTHONPATH: /app
      PYTHONUNBUFFERED: "1"
      PYTHONDONTWRITEBYTECODE: "1"
    volumes:
      # Persist SQLite database
      - uuid_data:/app/data
      # Persist logs
      - uuid_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - uuid_network

  # PostgreSQL Database (Optional - for production use)
  postgres:
    image: postgres:15-alpine
    container_name: uuid-management-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: uuid_management
      POSTGRES_USER: uuid_user
      POSTGRES_PASSWORD: uuid_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U uuid_user -d uuid_management"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - uuid_network
    profiles:
      - postgres

  # Redis (Optional - for future caching/session storage)
  redis:
    image: redis:7-alpine
    container_name: uuid-management-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - uuid_network
    profiles:
      - redis

  # Nginx Reverse Proxy (Optional - for production)
  nginx:
    image: nginx:alpine
    container_name: uuid-management-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - uuid-management
    networks:
      - uuid_network
    profiles:
      - nginx

volumes:
  # SQLite data persistence
  uuid_data:
    driver: local
  
  # Application logs
  uuid_logs:
    driver: local
  
  # PostgreSQL data (when using postgres profile)
  postgres_data:
    driver: local
  
  # Redis data (when using redis profile)
  redis_data:
    driver: local

networks:
  uuid_network:
    driver: bridge
