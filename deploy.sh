#!/bin/bash

# Deployment script for UUID Management System
# Supports development and production deployments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
DEPLOYMENT_TYPE="development"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} ✅ $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} ⚠️  $1"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} ❌ $1"
}

# Help function
show_help() {
    cat << EOF
UUID Management System - Deployment Script

Usage: $0 [COMMAND] [OPTIONS]

COMMANDS:
    start           Start the application
    stop            Stop the application
    restart         Restart the application
    status          Show application status
    logs            Show application logs
    backup          Create database backup
    update          Update and restart application
    clean           Clean up containers and volumes
    init            Initialize environment for first deployment

OPTIONS:
    -h, --help              Show this help message
    -e, --env ENV_FILE      Environment file (default: .env)
    -p, --production        Use production configuration
    -d, --development       Use development configuration (default)
    --postgres              Enable PostgreSQL
    --nginx                 Enable Nginx reverse proxy
    --redis                 Enable Redis
    --all-services          Enable all optional services

EXAMPLES:
    # Initialize for first deployment
    $0 init

    # Start development environment
    $0 start

    # Start production environment with all services
    $0 start --production --all-services

    # View logs
    $0 logs

    # Create backup
    $0 backup

    # Clean up everything
    $0 clean

EOF
}

# Parse command line arguments
COMMAND=""
PROFILES=""
PRODUCTION=false

while [[ $# -gt 0 ]]; do
    case $1 in
        start|stop|restart|status|logs|backup|update|clean|init)
            COMMAND="$1"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--env)
            ENV_FILE="$2"
            shift 2
            ;;
        -p|--production)
            PRODUCTION=true
            DEPLOYMENT_TYPE="production"
            shift
            ;;
        -d|--development)
            PRODUCTION=false
            DEPLOYMENT_TYPE="development"
            shift
            ;;
        --postgres)
            PROFILES="$PROFILES --profile postgres"
            shift
            ;;
        --nginx)
            PROFILES="$PROFILES --profile nginx"
            shift
            ;;
        --redis)
            PROFILES="$PROFILES --profile redis"
            shift
            ;;
        --all-services)
            PROFILES="--profile postgres --profile nginx --profile redis"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate command
if [ -z "$COMMAND" ]; then
    log_error "No command specified"
    show_help
    exit 1
fi

# Construct docker-compose command
construct_compose_cmd() {
    local cmd="docker-compose"
    
    # Add environment file
    if [ -f "$ENV_FILE" ]; then
        cmd="$cmd --env-file $ENV_FILE"
    fi
    
    # Add compose files
    cmd="$cmd -f $COMPOSE_FILE"
    
    if [ "$PRODUCTION" = true ]; then
        cmd="$cmd -f docker-compose.prod.yml"
    fi
    
    # Add profiles
    if [ -n "$PROFILES" ]; then
        cmd="$cmd $PROFILES"
    fi
    
    echo "$cmd"
}

# Initialize environment
init_environment() {
    log "Initializing environment for $DEPLOYMENT_TYPE deployment..."
    
    # Create .env file if it doesn't exist
    if [ ! -f "$ENV_FILE" ]; then
        log "Creating environment file from template..."
        cp .env.example "$ENV_FILE"
        log_success "Created $ENV_FILE from template"
        log_warning "Please edit $ENV_FILE with your configuration before starting"
    else
        log "Environment file $ENV_FILE already exists"
    fi
    
    # Create necessary directories
    log "Creating necessary directories..."
    mkdir -p data logs docker/nginx/ssl
    
    # Set permissions
    if [ -f "docker-entrypoint.sh" ]; then
        chmod +x docker-entrypoint.sh
    fi
    
    if [ -f "build-docker.sh" ]; then
        chmod +x build-docker.sh
    fi
    
    log_success "Environment initialization completed"
    
    if [ "$PRODUCTION" = true ]; then
        log_warning "Production deployment checklist:"
        log "  1. Update SECRET_KEY in $ENV_FILE"
        log "  2. Update admin credentials in $ENV_FILE"
        log "  3. Set DEBUG=false in $ENV_FILE"
        log "  4. Configure SSL certificates for Nginx (if using)"
        log "  5. Review security settings"
    fi
}

# Start application
start_application() {
    log "Starting UUID Management System ($DEPLOYMENT_TYPE mode)..."
    
    local compose_cmd=$(construct_compose_cmd)
    log "Executing: $compose_cmd up -d"
    
    if $compose_cmd up -d; then
        log_success "Application started successfully"
        
        # Wait for health check
        log "Waiting for application to be ready..."
        sleep 10
        
        # Check health
        if curl -f http://localhost:8000/health &> /dev/null; then
            log_success "Application is healthy and ready!"
            log "Web Interface: http://localhost:8000"
            log "API Documentation: http://localhost:8000/docs"
        else
            log_warning "Application started but health check failed"
            log "Check logs with: $0 logs"
        fi
    else
        log_error "Failed to start application"
        exit 1
    fi
}

# Stop application
stop_application() {
    log "Stopping UUID Management System..."
    
    local compose_cmd=$(construct_compose_cmd)
    
    if $compose_cmd down; then
        log_success "Application stopped successfully"
    else
        log_error "Failed to stop application"
        exit 1
    fi
}

# Restart application
restart_application() {
    log "Restarting UUID Management System..."
    stop_application
    sleep 2
    start_application
}

# Show status
show_status() {
    log "UUID Management System Status:"
    
    local compose_cmd=$(construct_compose_cmd)
    $compose_cmd ps
    
    echo ""
    log "Health Check:"
    if curl -f http://localhost:8000/health 2>/dev/null; then
        log_success "Application is healthy"
    else
        log_warning "Application is not responding"
    fi
}

# Show logs
show_logs() {
    log "Showing application logs..."
    
    local compose_cmd=$(construct_compose_cmd)
    $compose_cmd logs -f
}

# Create backup
create_backup() {
    log "Creating database backup..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="backups"
    
    mkdir -p "$backup_dir"
    
    # Check if using SQLite or PostgreSQL
    if grep -q "postgresql" "$ENV_FILE" 2>/dev/null; then
        log "Creating PostgreSQL backup..."
        local compose_cmd=$(construct_compose_cmd)
        $compose_cmd exec -T postgres pg_dump -U uuid_user uuid_management > "$backup_dir/backup_${timestamp}.sql"
        log_success "PostgreSQL backup created: $backup_dir/backup_${timestamp}.sql"
    else
        log "Creating SQLite backup..."
        if [ -f "data/uuid_management.db" ]; then
            cp "data/uuid_management.db" "$backup_dir/backup_${timestamp}.db"
            log_success "SQLite backup created: $backup_dir/backup_${timestamp}.db"
        else
            log_warning "SQLite database file not found"
        fi
    fi
}

# Update application
update_application() {
    log "Updating UUID Management System..."
    
    # Pull latest changes (if in git repo)
    if [ -d ".git" ]; then
        log "Pulling latest changes..."
        git pull
    fi
    
    # Rebuild and restart
    local compose_cmd=$(construct_compose_cmd)
    $compose_cmd build --no-cache
    restart_application
    
    log_success "Update completed"
}

# Clean up
clean_up() {
    log_warning "This will remove all containers, networks, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "Cleaning up..."
        
        local compose_cmd=$(construct_compose_cmd)
        $compose_cmd down -v --remove-orphans
        
        # Remove images
        docker image prune -f
        
        log_success "Cleanup completed"
    else
        log "Cleanup cancelled"
    fi
}

# Main execution
main() {
    log "🚀 UUID Management System Deployment Manager"
    log "============================================="
    log "Command: $COMMAND"
    log "Deployment Type: $DEPLOYMENT_TYPE"
    log "Environment File: $ENV_FILE"
    
    case $COMMAND in
        init)
            init_environment
            ;;
        start)
            start_application
            ;;
        stop)
            stop_application
            ;;
        restart)
            restart_application
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        backup)
            create_backup
            ;;
        update)
            update_application
            ;;
        clean)
            clean_up
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
