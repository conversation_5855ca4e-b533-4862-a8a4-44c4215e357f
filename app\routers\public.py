from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from ..crud import get_uuid, validate_and_activate_uuid
from ..schemas import UUIDValidationResponse, UUIDStatusResponse

router = APIRouter(
    prefix="",
    tags=["public"]
)


@router.get("/validate/{uuid}", response_model=UUIDValidationResponse)
async def validate_uuid(
    uuid: str,
    db: Session = Depends(get_db)
):
    """
    Validate and potentially activate a UUID.
    This endpoint can be called multiple times, but activation only happens once.
    """
    db_uuid, was_activated = validate_and_activate_uuid(db, uuid)
    
    if not db_uuid:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="UUID not found"
        )
    
    # Determine the message based on UUID state
    if not db_uuid.is_valid:
        if db_uuid.is_expired:
            message = "UUID has expired and cannot be activated"
        elif db_uuid.status == "inactive":
            message = "UUID is inactive and cannot be activated"
        else:
            message = "UUID is not valid"
    elif was_activated:
        message = "UUID validated and activated successfully via API"
    elif db_uuid.is_activated:
        activation_method = db_uuid.activation_method or "unknown"
        message = f"UUID is valid and was previously activated via {activation_method}"
    else:
        message = "UUID is valid but cannot be activated"

    return UUIDValidationResponse(
        uuid=db_uuid.uuid,
        is_valid=db_uuid.is_valid,
        is_activated=db_uuid.is_activated,
        is_expired=db_uuid.is_expired,
        status=db_uuid.status,
        effective_status=db_uuid.effective_status,
        message=message,
        activated_now=was_activated,
        activation_method=db_uuid.activation_method
    )


@router.get("/status/{uuid}", response_model=UUIDStatusResponse)
async def get_uuid_status(
    uuid: str,
    db: Session = Depends(get_db)
):
    """
    Get the current status of a UUID without modifying it.
    This is a read-only endpoint that doesn't increment usage count.
    """
    db_uuid = get_uuid(db, uuid, update_expiration=True)

    if not db_uuid:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="UUID not found"
        )

    return UUIDStatusResponse(
        uuid=db_uuid.uuid,
        status=db_uuid.status,
        effective_status=db_uuid.effective_status,
        is_valid=db_uuid.is_valid,
        is_activated=db_uuid.is_activated,
        is_expired=db_uuid.is_expired,
        usage_count=db_uuid.usage_count,
        created_at=db_uuid.created_at,
        last_used_at=db_uuid.last_used_at,
        activated_at=db_uuid.activated_at,
        expires_at=db_uuid.expires_at,
        activation_method=db_uuid.activation_method
    )
