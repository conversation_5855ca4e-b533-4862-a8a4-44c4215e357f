#!/usr/bin/env python3
"""
Test script to verify Docker setup for UUID Management System
"""

import os
import sys
import time
import requests
import subprocess
import json
from typing import Dict, Any

# Colors for output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def log(message: str, color: str = Colors.BLUE):
    """Log a message with color"""
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
    print(f"{color}[{timestamp}]{Colors.NC} {message}")

def log_success(message: str):
    """Log a success message"""
    log(f"✅ {message}", Colors.GREEN)

def log_warning(message: str):
    """Log a warning message"""
    log(f"⚠️  {message}", Colors.YELLOW)

def log_error(message: str):
    """Log an error message"""
    log(f"❌ {message}", Colors.RED)

def run_command(command: str, capture_output: bool = True) -> tuple:
    """Run a shell command and return (success, output)"""
    try:
        if capture_output:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            return result.returncode == 0, result.stdout.strip()
        else:
            result = subprocess.run(command, shell=True)
            return result.returncode == 0, ""
    except Exception as e:
        return False, str(e)

def check_docker_requirements() -> bool:
    """Check if Docker and Docker Compose are available"""
    log("Checking Docker requirements...")
    
    # Check Docker
    success, output = run_command("docker --version")
    if not success:
        log_error("Docker is not installed or not in PATH")
        return False
    log_success(f"Docker found: {output}")
    
    # Check Docker daemon
    success, output = run_command("docker info")
    if not success:
        log_error("Docker daemon is not running")
        return False
    log_success("Docker daemon is running")
    
    # Check Docker Compose
    success, output = run_command("docker-compose --version")
    if not success:
        log_error("Docker Compose is not installed or not in PATH")
        return False
    log_success(f"Docker Compose found: {output}")
    
    return True

def check_required_files() -> bool:
    """Check if all required Docker files exist"""
    log("Checking required files...")
    
    required_files = [
        "Dockerfile",
        "docker-compose.yml",
        "docker-entrypoint.sh",
        ".dockerignore",
        ".env.example"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            log_success(f"Found: {file}")
    
    if missing_files:
        log_error(f"Missing required files: {', '.join(missing_files)}")
        return False
    
    return True

def test_docker_build() -> bool:
    """Test building the Docker image"""
    log("Testing Docker image build...")
    
    # Build the image
    success, output = run_command("docker build -t uuid-management-test .", capture_output=False)
    if not success:
        log_error("Failed to build Docker image")
        return False
    
    log_success("Docker image built successfully")
    
    # Check if image exists
    success, output = run_command("docker images uuid-management-test")
    if not success or "uuid-management-test" not in output:
        log_error("Built image not found in Docker images")
        return False
    
    log_success("Docker image verified")
    return True

def test_container_startup() -> bool:
    """Test starting the container"""
    log("Testing container startup...")
    
    # Start container
    success, output = run_command(
        "docker run -d --name uuid-test -p 8001:8000 "
        "-e SECRET_KEY=test-secret-key "
        "-e DEBUG=true "
        "uuid-management-test"
    )
    
    if not success:
        log_error("Failed to start container")
        return False
    
    log_success("Container started")
    
    # Wait for startup
    log("Waiting for application to start...")
    time.sleep(15)
    
    return True

def test_application_health() -> bool:
    """Test if the application is responding"""
    log("Testing application health...")
    
    max_retries = 10
    for i in range(max_retries):
        try:
            response = requests.get("http://localhost:8001/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                log_success(f"Application is healthy: {health_data}")
                return True
        except requests.exceptions.RequestException:
            pass
        
        log(f"Health check attempt {i+1}/{max_retries} failed, retrying...")
        time.sleep(2)
    
    log_error("Application health check failed")
    return False

def test_api_endpoints() -> bool:
    """Test basic API endpoints"""
    log("Testing API endpoints...")
    
    base_url = "http://localhost:8001"
    
    # Test endpoints
    endpoints = [
        ("/", "Root endpoint"),
        ("/health", "Health check"),
        ("/docs", "API documentation"),
    ]
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5, allow_redirects=True)
            if response.status_code in [200, 302]:  # 302 for redirects
                log_success(f"{description}: {response.status_code}")
            else:
                log_warning(f"{description}: {response.status_code}")
        except requests.exceptions.RequestException as e:
            log_error(f"{description}: {e}")
            return False
    
    return True

def cleanup_test_resources():
    """Clean up test containers and images"""
    log("Cleaning up test resources...")
    
    # Stop and remove container
    run_command("docker stop uuid-test")
    run_command("docker rm uuid-test")
    
    # Remove test image
    run_command("docker rmi uuid-management-test")
    
    log_success("Cleanup completed")

def test_docker_compose() -> bool:
    """Test Docker Compose setup"""
    log("Testing Docker Compose setup...")
    
    # Check if .env exists, create from example if not
    if not os.path.exists(".env"):
        log("Creating .env from .env.example...")
        success, _ = run_command("cp .env.example .env")
        if not success:
            log_error("Failed to create .env file")
            return False
    
    # Test docker-compose config
    success, output = run_command("docker-compose config")
    if not success:
        log_error("Docker Compose configuration is invalid")
        return False
    
    log_success("Docker Compose configuration is valid")
    return True

def main():
    """Main test function"""
    log("🧪 Starting Docker setup tests for UUID Management System")
    log("=" * 60)
    
    tests = [
        ("Docker Requirements", check_docker_requirements),
        ("Required Files", check_required_files),
        ("Docker Compose Config", test_docker_compose),
        ("Docker Build", test_docker_build),
        ("Container Startup", test_container_startup),
        ("Application Health", test_application_health),
        ("API Endpoints", test_api_endpoints),
    ]
    
    passed = 0
    failed = 0
    
    try:
        for test_name, test_func in tests:
            log(f"\n🔍 Running test: {test_name}")
            log("-" * 40)
            
            if test_func():
                log_success(f"Test passed: {test_name}")
                passed += 1
            else:
                log_error(f"Test failed: {test_name}")
                failed += 1
                
                # Stop on critical failures
                if test_name in ["Docker Requirements", "Required Files"]:
                    log_error("Critical test failed, stopping...")
                    break
    
    finally:
        # Always cleanup
        cleanup_test_resources()
    
    # Summary
    log("\n" + "=" * 60)
    log(f"📊 Test Summary:")
    log(f"   Passed: {passed}")
    log(f"   Failed: {failed}")
    log(f"   Total:  {passed + failed}")
    
    if failed == 0:
        log_success("🎉 All tests passed! Docker setup is working correctly.")
        log("\nYou can now deploy using:")
        log("  ./deploy.sh init")
        log("  ./deploy.sh start")
        return True
    else:
        log_error(f"❌ {failed} test(s) failed. Please fix the issues before deploying.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
