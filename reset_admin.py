#!/usr/bin/env python3
"""
Reset admin password to default
"""

def reset_admin_password():
    """Reset admin password to default"""
    print("🔧 Resetting admin password...")
    
    try:
        from app.database import SessionLocal
        from app.crud import get_admin_user_by_username
        from app.auth import get_password_hash
        
        db = SessionLocal()
        try:
            # Get admin user
            admin_user = get_admin_user_by_username(db, "admin")
            if admin_user:
                print(f"   Found admin user: {admin_user.username}")
                print(f"   Current failed attempts: {admin_user.failed_login_attempts}")
                print(f"   Is locked: {admin_user.is_locked}")
                
                # Reset password to default
                default_password = "admin123!"
                new_hash = get_password_hash(default_password)
                
                admin_user.hashed_password = new_hash
                admin_user.failed_login_attempts = 0
                admin_user.locked_until = None
                
                db.commit()
                db.refresh(admin_user)
                
                print(f"   ✅ Password reset to: {default_password}")
                print(f"   ✅ Failed attempts reset to: {admin_user.failed_login_attempts}")
                print(f"   ✅ Account unlocked")
                
                return True
            else:
                print("   ❌ Admin user not found")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login():
    """Test login with default password"""
    print("🔑 Testing login with default password...")
    
    try:
        import requests
        
        data = {"username": "admin", "password": "admin123!"}
        response = requests.post("http://127.0.0.1:8000/admin/login", data=data, timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Login successful")
            return True
        else:
            print(f"   ❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Login test error: {e}")
        return False

def main():
    """Reset admin and test"""
    print("🔧 Admin Password Reset Utility")
    print("=" * 40)
    
    # Reset password
    if reset_admin_password():
        # Test login
        if test_login():
            print("\n✅ Admin password reset successful!")
            print("   Default credentials: admin / admin123!")
        else:
            print("\n❌ Password reset but login test failed")
    else:
        print("\n❌ Failed to reset admin password")

if __name__ == "__main__":
    main()
