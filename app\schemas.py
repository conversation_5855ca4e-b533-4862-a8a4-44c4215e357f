from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from datetime import datetime


class UUIDBase(BaseModel):
    description: Optional[str] = None
    expires_at: Optional[datetime] = None


class UUIDCreate(UUIDBase):
    """Schema for creating a new UUID"""
    pass


class UUIDUpdate(BaseModel):
    """Schema for updating an existing UUID"""
    description: Optional[str] = None
    expires_at: Optional[datetime] = None
    status: Optional[str] = Field(None, pattern="^(active|inactive)$")


class UUIDResponse(UUIDBase):
    """Schema for UUID response"""
    uuid: str
    created_at: datetime
    updated_at: Optional[datetime]
    last_used_at: Optional[datetime]
    activated_at: Optional[datetime]
    status: str
    effective_status: str
    usage_count: int
    is_activated: bool
    is_expired: bool
    is_valid: bool
    activation_method: Optional[str]
    created_by: str

    model_config = ConfigDict(from_attributes=True)


class UUIDValidationResponse(BaseModel):
    """Schema for UUID validation response"""
    uuid: str
    is_valid: bool
    is_activated: bool
    is_expired: bool
    status: str
    effective_status: str
    message: str
    activated_now: bool = False  # True if UUID was just activated
    activation_method: Optional[str] = None


class UUIDStatusResponse(BaseModel):
    """Schema for UUID status check response"""
    uuid: str
    status: str
    effective_status: str
    is_valid: bool
    is_activated: bool
    is_expired: bool
    usage_count: int
    created_at: datetime
    last_used_at: Optional[datetime]
    activated_at: Optional[datetime]
    expires_at: Optional[datetime]
    activation_method: Optional[str]


class Token(BaseModel):
    """Schema for authentication token"""
    access_token: str
    token_type: str


class TokenData(BaseModel):
    """Schema for token data"""
    username: Optional[str] = None


class User(BaseModel):
    """Schema for user"""
    username: str
    is_admin: bool = True


class UserInDB(User):
    """Schema for user in database"""
    hashed_password: str


class LoginRequest(BaseModel):
    """Schema for login request"""
    username: str
    password: str


class UUIDListResponse(BaseModel):
    """Schema for UUID list response"""
    total: int
    uuids: list[UUIDResponse]


class UUIDStatsResponse(BaseModel):
    """Schema for UUID statistics response"""
    total_uuids: int
    active_uuids: int
    inactive_uuids: int
    expired_uuids: int
    activated_uuids: int
    total_usage: int


class UUIDActivationRequest(BaseModel):
    """Schema for admin UUID activation request"""
    pass  # No additional data needed, just the UUID from the path


class UUIDActivationResponse(BaseModel):
    """Schema for UUID activation response"""
    uuid: str
    success: bool
    message: str
    was_already_activated: bool
    activation_method: Optional[str]
    activated_at: Optional[datetime]


class PasswordChangeRequest(BaseModel):
    """Schema for password change request"""
    current_password: str = Field(..., min_length=1, description="Current password")
    new_password: str = Field(..., min_length=1, description="New password")
    confirm_password: str = Field(..., min_length=1, description="Confirm new password")

    def validate_passwords_match(self) -> bool:
        """Validate that new password and confirmation match"""
        return self.new_password == self.confirm_password

    def validate_password_strength(self) -> tuple[bool, list[str]]:
        """Validate password strength requirements"""
        errors = []
        password = self.new_password

        # Minimum length
        if len(password) < 8:
            errors.append("Password must be at least 8 characters long")

        # Maximum length
        if len(password) > 128:
            errors.append("Password must be no more than 128 characters long")

        # Must contain at least one letter
        if not any(c.isalpha() for c in password):
            errors.append("Password must contain at least one letter")

        # Must contain at least one number
        if not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one number")

        # Must contain at least one special character
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            errors.append("Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)")

        # Cannot be same as current password
        if password == self.current_password:
            errors.append("New password must be different from current password")

        return len(errors) == 0, errors


class PasswordChangeResponse(BaseModel):
    """Schema for password change response"""
    success: bool
    message: str
    changed_at: Optional[datetime] = None


class AdminUserResponse(BaseModel):
    """Schema for admin user response"""
    id: int
    username: str
    is_admin: bool
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    last_login_at: Optional[datetime]
    password_changed_at: Optional[datetime]
    failed_login_attempts: int
    is_locked: bool

    model_config = ConfigDict(from_attributes=True)


class AdminSettingsResponse(BaseModel):
    """Schema for admin settings response"""
    user: AdminUserResponse
    password_requirements: dict = {
        "min_length": 8,
        "max_length": 128,
        "require_letters": True,
        "require_numbers": True,
        "require_special_chars": True,
        "special_chars": "!@#$%^&*()_+-=[]{}|;:,.<>?"
    }
