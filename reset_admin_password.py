#!/usr/bin/env python3
"""
Reset admin password to default
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.crud import get_admin_user_by_username
from app.auth import get_password_hash

def reset_admin_password():
    """Reset admin password to default"""
    db = SessionLocal()
    try:
        # Get admin user
        admin_user = get_admin_user_by_username(db, "admin")
        if not admin_user:
            print("❌ Admin user not found")
            return False
        
        # Reset password to default
        default_password = "admin123"
        hashed_password = get_password_hash(default_password)
        
        admin_user.update_password(hashed_password)
        db.commit()
        
        print("✅ Admin password reset to default (admin123)")
        return True
        
    except Exception as e:
        print(f"❌ Error resetting password: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    reset_admin_password()
