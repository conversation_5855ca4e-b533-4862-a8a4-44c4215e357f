from sqlalchemy import Column, String, DateTime, Inte<PERSON>, Boolean, Text
from sqlalchemy.sql import func
from datetime import datetime, timedelta
from .database import Base
import uuid as uuid_lib


class UUID(Base):
    __tablename__ = "uuids"
    
    # Primary key - the UUID value itself
    uuid = Column(String(36), primary_key=True, index=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    activated_at = Column(DateTime(timezone=True), nullable=True)  # When UUID was first activated
    
    # Status and usage tracking
    status = Column(String(20), default="active", nullable=False)  # active, inactive, expired
    usage_count = Column(Integer, default=0, nullable=False)
    is_activated = Column(Bo<PERSON>an, default=False, nullable=False)  # Can only be activated once
    activation_method = Column(String(20), nullable=True)  # 'api' or 'admin' - how it was activated

    # Optional metadata
    description = Column(Text, nullable=True)
    created_by = Column(String(100), default="admin", nullable=False)
    
    def __init__(self, **kwargs):
        if 'uuid' not in kwargs:
            kwargs['uuid'] = str(uuid_lib.uuid4())
        super().__init__(**kwargs)
    
    @property
    def is_expired(self) -> bool:
        """Check if UUID has expired"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at

    @property
    def effective_status(self) -> str:
        """Get the effective status with expiration priority"""
        # Expired status takes precedence over all other statuses
        if self.is_expired:
            return "expired"
        return self.status

    @property
    def is_valid(self) -> bool:
        """Check if UUID is valid for use - considers expiration and status"""
        # Expired UUIDs are never valid
        if self.is_expired:
            return False
        # Only active UUIDs are valid
        return self.status == "active"

    def can_be_activated(self) -> bool:
        """Check if UUID can be activated (only once)"""
        # Cannot activate if already activated
        if self.is_activated:
            return False
        # Cannot activate expired UUIDs
        if self.is_expired:
            return False
        # Can only activate active UUIDs
        return self.status == "active"

    def activate(self, method: str = "api"):
        """
        Activate the UUID (can only be done once)
        method: 'api' for automatic activation via validation, 'admin' for manual activation
        """
        if self.can_be_activated():
            self.is_activated = True
            self.activated_at = datetime.utcnow()
            self.activation_method = method
            return True
        return False
    
    def increment_usage(self):
        """Increment usage count and update last used timestamp"""
        self.usage_count += 1
        self.last_used_at = datetime.utcnow()

    def update_expiration_status(self):
        """
        Update the status to 'expired' if the UUID has expired.
        Returns True if status was changed, False otherwise.
        """
        if self.is_expired and self.status != "expired":
            self.status = "expired"
            self.updated_at = datetime.utcnow()
            return True
        return False

    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "uuid": self.uuid,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "activated_at": self.activated_at.isoformat() if self.activated_at else None,
            "status": self.status,
            "effective_status": self.effective_status,
            "usage_count": self.usage_count,
            "is_activated": self.is_activated,
            "is_expired": self.is_expired,
            "is_valid": self.is_valid,
            "activation_method": self.activation_method,
            "description": self.description,
            "created_by": self.created_by
        }


class AdminUser(Base):
    __tablename__ = "admin_users"

    # Primary key
    id = Column(Integer, primary_key=True, index=True)

    # User credentials
    username = Column(String(50), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)

    # User metadata
    is_admin = Column(Boolean, default=True, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    password_changed_at = Column(DateTime(timezone=True), nullable=True)

    # Security fields
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime(timezone=True), nullable=True)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @property
    def is_locked(self) -> bool:
        """Check if account is locked due to failed login attempts"""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until

    def lock_account(self, duration_minutes: int = 30):
        """Lock account for specified duration"""
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.updated_at = datetime.utcnow()

    def unlock_account(self):
        """Unlock account and reset failed attempts"""
        self.locked_until = None
        self.failed_login_attempts = 0
        self.updated_at = datetime.utcnow()

    def record_failed_login(self):
        """Record a failed login attempt"""
        self.failed_login_attempts += 1
        self.updated_at = datetime.utcnow()

        # Lock account after 5 failed attempts
        if self.failed_login_attempts >= 5:
            self.lock_account()

    def record_successful_login(self):
        """Record a successful login"""
        self.last_login_at = datetime.utcnow()
        self.failed_login_attempts = 0
        self.locked_until = None
        self.updated_at = datetime.utcnow()

    def update_password(self, hashed_password: str):
        """Update password and record timestamp"""
        self.hashed_password = hashed_password
        self.password_changed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    def to_dict(self):
        """Convert model to dictionary (excluding sensitive data)"""
        return {
            "id": self.id,
            "username": self.username,
            "is_admin": self.is_admin,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_login_at": self.last_login_at.isoformat() if self.last_login_at else None,
            "password_changed_at": self.password_changed_at.isoformat() if self.password_changed_at else None,
            "failed_login_attempts": self.failed_login_attempts,
            "is_locked": self.is_locked
        }
