#!/usr/bin/env python3
"""
Debug password reset issue
"""

import requests
import time

BASE_URL = "http://127.0.0.1:8000"

def test_password_change_cycle():
    """Test complete password change cycle"""
    print("🔄 Testing Password Change Cycle")
    print("=" * 40)
    
    # Step 1: Login with default password
    print("1. 🔑 Login with default password...")
    login_data = {"username": "admin", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/admin/login", data=login_data)
    
    if response.status_code != 200:
        print(f"   ❌ Login failed: {response.status_code}")
        print(f"   Response: {response.text}")
        return False
    
    token1 = response.json()['access_token']
    print("   ✅ Login successful")
    
    # Step 2: Change password to new value
    print("2. 🔄 Change password to new value...")
    new_password = f"TestPass{int(time.time())}!"
    
    headers1 = {
        "Authorization": f"Bearer {token1}",
        "Content-Type": "application/json"
    }
    
    change_data = {
        "current_password": "admin123",
        "new_password": new_password,
        "confirm_password": new_password
    }
    
    response = requests.post(f"{BASE_URL}/admin/change-password", headers=headers1, json=change_data)
    
    if response.status_code != 200:
        print(f"   ❌ Password change failed: {response.status_code}")
        print(f"   Response: {response.text}")
        return False
    
    print("   ✅ Password changed successfully")
    print(f"   New password: {new_password}")
    
    # Step 3: Login with new password
    print("3. 🔑 Login with new password...")
    login_data2 = {"username": "admin", "password": new_password}
    response = requests.post(f"{BASE_URL}/admin/login", data=login_data2)
    
    if response.status_code != 200:
        print(f"   ❌ Login with new password failed: {response.status_code}")
        print(f"   Response: {response.text}")
        return False
    
    token2 = response.json()['access_token']
    print("   ✅ Login with new password successful")
    
    # Step 4: Try to change password back to default
    print("4. 🔄 Change password back to default...")
    
    headers2 = {
        "Authorization": f"Bearer {token2}",
        "Content-Type": "application/json"
    }
    
    reset_data = {
        "current_password": new_password,
        "new_password": "admin123",
        "confirm_password": "admin123"
    }
    
    print(f"   Current password: {new_password}")
    print(f"   New password: admin123")
    
    response = requests.post(f"{BASE_URL}/admin/change-password", headers=headers2, json=reset_data)
    
    if response.status_code != 200:
        print(f"   ❌ Password reset failed: {response.status_code}")
        print(f"   Response: {response.text}")
        
        # Try to understand the error
        try:
            error_data = response.json()
            print(f"   Error detail: {error_data.get('detail', 'No detail')}")
        except:
            pass
        
        return False
    
    print("   ✅ Password reset successful")
    
    # Step 5: Verify login with default password
    print("5. 🔑 Verify login with default password...")
    login_data3 = {"username": "admin", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/admin/login", data=login_data3)
    
    if response.status_code != 200:
        print(f"   ❌ Login verification failed: {response.status_code}")
        return False
    
    print("   ✅ Login verification successful")
    print("\n✅ Complete password change cycle successful!")
    return True

def test_validation_logic():
    """Test the validation logic directly"""
    print("\n🧪 Testing Validation Logic")
    print("=" * 30)
    
    # Test the schema validation
    from app.schemas import PasswordChangeRequest
    
    # Test case 1: Different passwords (should pass)
    print("1. Testing different passwords...")
    request1 = PasswordChangeRequest(
        current_password="admin123",
        new_password="NewPassword123!",
        confirm_password="NewPassword123!"
    )
    
    is_valid, errors = request1.validate_password_strength()
    print(f"   Valid: {is_valid}")
    if not is_valid:
        print(f"   Errors: {errors}")
    
    # Test case 2: Same passwords (should fail)
    print("2. Testing same passwords...")
    request2 = PasswordChangeRequest(
        current_password="admin123",
        new_password="admin123",
        confirm_password="admin123"
    )
    
    is_valid, errors = request2.validate_password_strength()
    print(f"   Valid: {is_valid}")
    if not is_valid:
        print(f"   Errors: {errors}")
    
    # Test case 3: Reset scenario (should pass)
    print("3. Testing reset scenario...")
    request3 = PasswordChangeRequest(
        current_password="NewPassword123!",
        new_password="admin123",
        confirm_password="admin123"
    )
    
    is_valid, errors = request3.validate_password_strength()
    print(f"   Valid: {is_valid}")
    if not is_valid:
        print(f"   Errors: {errors}")

def main():
    """Run debug tests"""
    print("🐛 Password Reset Debug")
    print("=" * 50)
    
    # Test validation logic
    test_validation_logic()
    
    # Test actual password change cycle
    success = test_password_change_cycle()
    
    if not success:
        print("\n❌ Password change cycle failed")
    
    return success

if __name__ == "__main__":
    main()
