#!/usr/bin/env python3
"""
Test script for UUID Lifecycle Management Features
Demonstrates the new lifecycle management functionality
"""

import requests
import json
import time
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def get_admin_token():
    """Get admin authentication token"""
    data = {"username": "admin", "password": "admin123"}
    response = requests.post(f"{BASE_URL}/admin/login", data=data)
    return response.json()['access_token']

def test_expiration_lifecycle():
    """Test automatic expiration status updates"""
    print("🕒 Testing UUID Expiration Lifecycle...")
    
    token = get_admin_token()
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Create UUID that expires in 2 seconds
    future_date = (datetime.now() + timedelta(seconds=2)).isoformat()
    data = {
        "description": "UUID that expires quickly",
        "expires_at": future_date
    }
    
    response = requests.post(f"{BASE_URL}/admin/uuids", headers=headers, json=data)
    uuid_value = response.json()['uuid']
    print(f"Created UUID: {uuid_value}")
    print(f"Expires at: {future_date}")
    
    # Check initial status
    print("\n📊 Initial status:")
    response = requests.get(f"{BASE_URL}/status/{uuid_value}")
    result = response.json()
    print(f"Status: {result['status']}")
    print(f"Effective Status: {result['effective_status']}")
    print(f"Is Valid: {result['is_valid']}")
    print(f"Is Expired: {result['is_expired']}")
    
    # Wait for expiration
    print("\n⏳ Waiting for UUID to expire...")
    time.sleep(3)
    
    # Check status after expiration
    print("\n📊 Status after expiration:")
    response = requests.get(f"{BASE_URL}/status/{uuid_value}")
    result = response.json()
    print(f"Status: {result['status']}")
    print(f"Effective Status: {result['effective_status']}")
    print(f"Is Valid: {result['is_valid']}")
    print(f"Is Expired: {result['is_expired']}")
    
    # Try to validate expired UUID
    print("\n❌ Attempting to validate expired UUID:")
    response = requests.get(f"{BASE_URL}/validate/{uuid_value}")
    result = response.json()
    print(f"Message: {result['message']}")
    print(f"Activated: {result['is_activated']}")
    print(f"Activated Now: {result['activated_now']}")
    
    print()
    return uuid_value

def test_admin_activation():
    """Test admin manual activation"""
    print("👨‍💼 Testing Admin Manual Activation...")
    
    token = get_admin_token()
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Create a new UUID
    data = {"description": "UUID for admin activation test"}
    response = requests.post(f"{BASE_URL}/admin/uuids", headers=headers, json=data)
    uuid_value = response.json()['uuid']
    print(f"Created UUID: {uuid_value}")
    
    # Check initial activation status
    response = requests.get(f"{BASE_URL}/status/{uuid_value}")
    result = response.json()
    print(f"Initially activated: {result['is_activated']}")
    
    # Admin activate the UUID
    print("\n🔧 Admin activating UUID...")
    response = requests.post(f"{BASE_URL}/admin/uuids/{uuid_value}/activate", headers=headers)
    result = response.json()
    print(f"Activation success: {result['success']}")
    print(f"Message: {result['message']}")
    print(f"Activation method: {result['activation_method']}")
    
    # Check status after admin activation
    response = requests.get(f"{BASE_URL}/status/{uuid_value}")
    result = response.json()
    print(f"Now activated: {result['is_activated']}")
    print(f"Activation method: {result['activation_method']}")
    print(f"Activated at: {result['activated_at']}")
    
    # Try to activate again (should fail)
    print("\n🚫 Attempting to activate again (should fail):")
    response = requests.post(f"{BASE_URL}/admin/uuids/{uuid_value}/activate", headers=headers)
    result = response.json()
    print(f"Activation success: {result['success']}")
    print(f"Message: {result['message']}")
    print(f"Was already activated: {result['was_already_activated']}")
    
    print()
    return uuid_value

def test_api_vs_admin_activation():
    """Test difference between API and admin activation"""
    print("🔄 Testing API vs Admin Activation Methods...")
    
    token = get_admin_token()
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Create two UUIDs
    print("Creating two UUIDs for comparison...")
    
    # UUID 1 - for API activation
    data = {"description": "UUID for API activation"}
    response = requests.post(f"{BASE_URL}/admin/uuids", headers=headers, json=data)
    uuid_api = response.json()['uuid']
    
    # UUID 2 - for admin activation
    data = {"description": "UUID for admin activation"}
    response = requests.post(f"{BASE_URL}/admin/uuids", headers=headers, json=data)
    uuid_admin = response.json()['uuid']
    
    print(f"API UUID: {uuid_api}")
    print(f"Admin UUID: {uuid_admin}")
    
    # Activate via API (validation endpoint)
    print("\n🌐 Activating first UUID via API validation...")
    response = requests.get(f"{BASE_URL}/validate/{uuid_api}")
    result = response.json()
    print(f"Message: {result['message']}")
    print(f"Activation method: {result['activation_method']}")
    
    # Activate via admin interface
    print("\n👨‍💼 Activating second UUID via admin interface...")
    response = requests.post(f"{BASE_URL}/admin/uuids/{uuid_admin}/activate", headers=headers)
    result = response.json()
    print(f"Message: {result['message']}")
    print(f"Activation method: {result['activation_method']}")
    
    # Compare final states
    print("\n📊 Comparing final states:")
    
    response = requests.get(f"{BASE_URL}/status/{uuid_api}")
    api_result = response.json()
    
    response = requests.get(f"{BASE_URL}/status/{uuid_admin}")
    admin_result = response.json()
    
    print(f"API UUID - Method: {api_result['activation_method']}, Activated: {api_result['is_activated']}")
    print(f"Admin UUID - Method: {admin_result['activation_method']}, Activated: {admin_result['is_activated']}")
    
    print()

def test_status_priority_logic():
    """Test status priority logic (expired takes precedence)"""
    print("⚖️ Testing Status Priority Logic...")
    
    token = get_admin_token()
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Create UUID that expires quickly but is active
    future_date = (datetime.now() + timedelta(seconds=2)).isoformat()
    data = {
        "description": "Active UUID that will expire",
        "expires_at": future_date
    }
    
    response = requests.post(f"{BASE_URL}/admin/uuids", headers=headers, json=data)
    uuid_value = response.json()['uuid']
    print(f"Created active UUID: {uuid_value}")
    
    # Check initial status
    response = requests.get(f"{BASE_URL}/status/{uuid_value}")
    result = response.json()
    print(f"Initial - Status: {result['status']}, Effective: {result['effective_status']}, Valid: {result['is_valid']}")
    
    # Wait for expiration
    print("\n⏳ Waiting for expiration...")
    time.sleep(3)
    
    # Check status after expiration (should show expired takes precedence)
    response = requests.get(f"{BASE_URL}/status/{uuid_value}")
    result = response.json()
    print(f"After expiration - Status: {result['status']}, Effective: {result['effective_status']}, Valid: {result['is_valid']}")
    
    print("✅ Expired status takes precedence over active status")
    print()

def test_unlimited_validation():
    """Test unlimited validation attempts"""
    print("🔄 Testing Unlimited Validation Attempts...")
    
    token = get_admin_token()
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Create UUID
    data = {"description": "UUID for unlimited validation test"}
    response = requests.post(f"{BASE_URL}/admin/uuids", headers=headers, json=data)
    uuid_value = response.json()['uuid']
    print(f"Created UUID: {uuid_value}")
    
    # Validate multiple times
    for i in range(1, 6):
        print(f"\n🔍 Validation attempt #{i}:")
        response = requests.get(f"{BASE_URL}/validate/{uuid_value}")
        result = response.json()
        print(f"  Activated now: {result['activated_now']}")
        print(f"  Is activated: {result['is_activated']}")
        print(f"  Message: {result['message']}")
        
        # Check usage count
        response = requests.get(f"{BASE_URL}/status/{uuid_value}")
        status_result = response.json()
        print(f"  Usage count: {status_result['usage_count']}")
    
    print("✅ UUID can be validated unlimited times, but activated only once")
    print()

def main():
    """Run all lifecycle tests"""
    print("🚀 Starting UUID Lifecycle Management Tests\n")
    
    try:
        # Test expiration lifecycle
        test_expiration_lifecycle()
        
        # Test admin activation
        test_admin_activation()
        
        # Test API vs admin activation
        test_api_vs_admin_activation()
        
        # Test status priority logic
        test_status_priority_logic()
        
        # Test unlimited validation
        test_unlimited_validation()
        
        print("✅ All lifecycle tests completed successfully!")
        print("\n📋 Summary of new features tested:")
        print("  ✓ Automatic expiration status updates")
        print("  ✓ One-time activation rule (admin and API)")
        print("  ✓ Activation method tracking")
        print("  ✓ Status priority logic (expired takes precedence)")
        print("  ✓ Unlimited validation attempts")
        print("  ✓ Enhanced status reporting")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
