# Environment Configuration Template for UUID Management System
# Copy this file to .env and customize the values for your deployment

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# SQLite (Development/Small deployments)
DATABASE_URL=sqlite:///./data/uuid_management.db

# PostgreSQL (Production recommended)
# DATABASE_URL=*********************************************************/uuid_management

# PostgreSQL connection details (when using docker-compose with postgres)
POSTGRES_USER=uuid_user
POSTGRES_PASSWORD=your_secure_postgres_password
POSTGRES_DB=uuid_management

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Secret Key - MUST be changed in production!
# Generate with: openssl rand -hex 32
SECRET_KEY=your-super-secret-key-change-this-in-production

# JWT Algorithm
ALGORITHM=HS256

# Token expiration time in minutes
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================

# Default admin credentials - MUST be changed in production!
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123!

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application name and version
APP_NAME=UUID Management System
APP_VERSION=1.0.0

# Debug mode - set to false in production
DEBUG=true

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Python configuration
PYTHONPATH=/app
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# =============================================================================
# OPTIONAL SERVICES CONFIGURATION
# =============================================================================

# Redis (if using redis profile)
REDIS_URL=redis://redis:6379/0

# =============================================================================
# PRODUCTION SECURITY NOTES
# =============================================================================

# For production deployment:
# 1. Generate a strong SECRET_KEY:
#    openssl rand -hex 32
#
# 2. Use strong passwords for ADMIN_PASSWORD and POSTGRES_PASSWORD
#
# 3. Set DEBUG=false
#
# 4. Use PostgreSQL instead of SQLite for better performance and reliability
#
# 5. Consider using environment variable injection from your deployment platform
#    instead of .env files for sensitive values
#
# 6. Enable SSL/TLS in nginx configuration
#
# 7. Set up proper firewall rules and network security

# =============================================================================
# EXAMPLE PRODUCTION VALUES (customize these)
# =============================================================================

# SECRET_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
# ADMIN_USERNAME=your_admin_username
# ADMIN_PASSWORD=YourVerySecurePassword123!@#
# POSTGRES_PASSWORD=AnotherVerySecurePassword456$%^
# DEBUG=false
