from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from typing import List, Optional, Tuple
from datetime import datetime
from .models import UUID, AdminUser
from .schemas import UUIDCreate, UUIDUpdate


def create_uuid(db: Session, uuid_data: UUIDCreate, created_by: str = "admin") -> UUID:
    """Create a new UUID"""
    db_uuid = UUID(
        description=uuid_data.description,
        expires_at=uuid_data.expires_at,
        created_by=created_by
    )
    db.add(db_uuid)
    db.commit()
    db.refresh(db_uuid)
    return db_uuid


def get_uuid(db: Session, uuid: str, update_expiration: bool = True) -> Optional[UUID]:
    """
    Get a UUID by its value
    update_expiration: If True, automatically update status to 'expired' if expired
    """
    db_uuid = db.query(UUID).filter(UUID.uuid == uuid).first()

    if db_uuid and update_expiration:
        # Automatically update expiration status
        if db_uuid.update_expiration_status():
            db.commit()
            db.refresh(db_uuid)

    return db_uuid


def get_uuids(db: Session, skip: int = 0, limit: int = 100) -> List[UUID]:
    """Get all UUIDs with pagination"""
    return db.query(UUID).offset(skip).limit(limit).all()


def update_uuid(db: Session, uuid: str, uuid_update: UUIDUpdate) -> Optional[UUID]:
    """Update a UUID"""
    db_uuid = get_uuid(db, uuid)
    if not db_uuid:
        return None
    
    update_data = uuid_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_uuid, field, value)
    
    db_uuid.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_uuid)
    return db_uuid


def delete_uuid(db: Session, uuid: str) -> bool:
    """Delete a UUID"""
    db_uuid = get_uuid(db, uuid)
    if not db_uuid:
        return False
    
    db.delete(db_uuid)
    db.commit()
    return True


def validate_and_activate_uuid(db: Session, uuid: str) -> tuple[Optional[UUID], bool]:
    """
    Validate a UUID and activate it if possible via API
    Returns (uuid_object, was_activated)
    """
    db_uuid = get_uuid(db, uuid, update_expiration=True)
    if not db_uuid:
        return None, False

    # Increment usage count regardless of activation status
    db_uuid.increment_usage()

    # Try to activate if not already activated (using 'api' method)
    was_activated = False
    if db_uuid.can_be_activated():
        was_activated = db_uuid.activate(method="api")

    db.commit()
    db.refresh(db_uuid)
    return db_uuid, was_activated


def admin_activate_uuid(db: Session, uuid: str) -> tuple[Optional[UUID], bool, str]:
    """
    Manually activate a UUID via admin interface
    Returns (uuid_object, was_activated, message)
    """
    db_uuid = get_uuid(db, uuid, update_expiration=True)
    if not db_uuid:
        return None, False, "UUID not found"

    # Check if already activated
    if db_uuid.is_activated:
        return db_uuid, False, f"UUID was already activated via {db_uuid.activation_method} on {db_uuid.activated_at}"

    # Try to activate using admin method
    was_activated = db_uuid.activate(method="admin")

    if was_activated:
        db.commit()
        db.refresh(db_uuid)
        return db_uuid, True, "UUID activated successfully by admin"
    else:
        # Determine why activation failed
        if db_uuid.is_expired:
            message = "Cannot activate expired UUID"
        elif db_uuid.status != "active":
            message = f"Cannot activate UUID with status '{db_uuid.status}'"
        else:
            message = "UUID cannot be activated"

        return db_uuid, False, message


def get_uuid_stats(db: Session) -> dict:
    """Get UUID statistics"""
    total = db.query(UUID).count()
    active = db.query(UUID).filter(UUID.status == "active").count()
    inactive = db.query(UUID).filter(UUID.status == "inactive").count()
    
    # Count expired UUIDs
    now = datetime.utcnow()
    expired = db.query(UUID).filter(
        and_(
            UUID.expires_at.isnot(None),
            UUID.expires_at < now
        )
    ).count()
    
    activated = db.query(UUID).filter(UUID.is_activated == True).count()
    total_usage = db.query(func.sum(UUID.usage_count)).scalar() or 0
    
    return {
        "total_uuids": total,
        "active_uuids": active,
        "inactive_uuids": inactive,
        "expired_uuids": expired,
        "activated_uuids": activated,
        "total_usage": total_usage
    }


def search_uuids(
    db: Session,
    status: Optional[str] = None,
    is_activated: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100
) -> List[UUID]:
    """Search UUIDs with filters"""
    query = db.query(UUID)

    if status:
        query = query.filter(UUID.status == status)

    if is_activated is not None:
        query = query.filter(UUID.is_activated == is_activated)

    return query.offset(skip).limit(limit).all()


def update_expired_uuids(db: Session) -> int:
    """
    Bulk update all expired UUIDs to have status 'expired'
    Returns the number of UUIDs updated
    """
    now = datetime.utcnow()

    # Find all UUIDs that are expired but not marked as expired
    expired_uuids = db.query(UUID).filter(
        and_(
            UUID.expires_at.isnot(None),
            UUID.expires_at < now,
            UUID.status != "expired"
        )
    ).all()

    count = 0
    for uuid_obj in expired_uuids:
        if uuid_obj.update_expiration_status():
            count += 1

    if count > 0:
        db.commit()

    return count


def get_uuids_with_expiration_check(db: Session, skip: int = 0, limit: int = 100) -> List[UUID]:
    """Get UUIDs with automatic expiration status updates"""
    # First, update any expired UUIDs
    update_expired_uuids(db)

    # Then return the requested UUIDs
    return get_uuids(db, skip=skip, limit=limit)


# ===== ADMIN USER CRUD OPERATIONS =====

def get_admin_user_by_username(db: Session, username: str) -> Optional[AdminUser]:
    """Get admin user by username"""
    return db.query(AdminUser).filter(AdminUser.username == username).first()


def get_admin_user_by_id(db: Session, user_id: int) -> Optional[AdminUser]:
    """Get admin user by ID"""
    return db.query(AdminUser).filter(AdminUser.id == user_id).first()


def create_admin_user(db: Session, username: str, hashed_password: str) -> AdminUser:
    """Create a new admin user"""
    from .auth import get_password_hash

    db_user = AdminUser(
        username=username,
        hashed_password=hashed_password,
        is_admin=True,
        is_active=True
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def authenticate_admin_user(db: Session, username: str, password: str) -> Optional[AdminUser]:
    """Authenticate admin user with database"""
    from .auth import verify_password

    user = get_admin_user_by_username(db, username)
    if not user:
        return None

    # Check if account is locked
    if user.is_locked:
        return None

    # Check if account is active
    if not user.is_active:
        return None

    # Verify password
    if not verify_password(password, user.hashed_password):
        # Record failed login attempt
        user.record_failed_login()
        db.commit()
        return None

    # Record successful login
    user.record_successful_login()
    db.commit()
    db.refresh(user)
    return user


def change_admin_password(
    db: Session,
    user_id: int,
    current_password: str,
    new_password: str
) -> Tuple[bool, str, Optional[AdminUser]]:
    """
    Change admin user password
    Returns (success, message, user_object)
    """
    from .auth import verify_password, get_password_hash

    # Get user
    user = get_admin_user_by_id(db, user_id)
    if not user:
        return False, "User not found", None

    # Check if account is locked
    if user.is_locked:
        return False, "Account is locked due to failed login attempts", None

    # Verify current password
    if not verify_password(current_password, user.hashed_password):
        user.record_failed_login()
        db.commit()
        return False, "Current password is incorrect", None

    # Hash new password
    new_hashed_password = get_password_hash(new_password)

    # Update password
    user.update_password(new_hashed_password)
    db.commit()
    db.refresh(user)

    return True, "Password changed successfully", user


def initialize_default_admin(db: Session) -> AdminUser:
    """Initialize default admin user if none exists"""
    from .auth import get_password_hash
    from .config import settings

    # Check if any admin user exists
    existing_admin = db.query(AdminUser).first()
    if existing_admin:
        return existing_admin

    # Create default admin user
    default_admin = create_admin_user(
        db=db,
        username=settings.admin_username,
        hashed_password=get_password_hash(settings.admin_password)
    )

    return default_admin
