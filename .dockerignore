# Docker ignore file for UUID Management System

# Git
.git
.gitignore
.gitattributes

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
*.md
docs/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
log/

# Database files (will be created in container)
*.db
*.sqlite
*.sqlite3

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Test files
test_*.py
*_test.py
tests/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Cache
.cache/
.mypy_cache/
.dmypy.json
dmypy.json

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Debug and development scripts
debug_*.py
simple_test.py
check_*.py
reset_*.py
migrate_*.py

# Development utilities
test_*.py
