from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session
from .config import settings
from .schemas import TokenD<PERSON>, User, UserInDB
from .database import get_db

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Fake user database (in production, use a real database)
fake_users_db = {
    settings.admin_username: {
        "username": settings.admin_username,
        "hashed_password": pwd_context.hash(settings.admin_password),
        "is_admin": True,
    }
}


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)


def get_user(username: str, db: Optional[Session] = None) -> Optional[UserInDB]:
    """Get user from database or fallback to fake database"""
    # Try database first if available
    if db is not None:
        from .crud import get_admin_user_by_username
        db_user = get_admin_user_by_username(db, username)
        if db_user:
            return UserInDB(
                username=db_user.username,
                hashed_password=db_user.hashed_password,
                is_admin=db_user.is_admin
            )

    # Fallback to fake database for backward compatibility
    if username in fake_users_db:
        user_dict = fake_users_db[username]
        return UserInDB(**user_dict)
    return None


def authenticate_user(username: str, password: str, db: Optional[Session] = None) -> Optional[UserInDB]:
    """Authenticate a user with database or fallback"""
    # Try database authentication first if available
    if db is not None:
        from .crud import authenticate_admin_user
        db_user = authenticate_admin_user(db, username, password)
        if db_user:
            return UserInDB(
                username=db_user.username,
                hashed_password=db_user.hashed_password,
                is_admin=db_user.is_admin
            )
        # If database authentication fails, don't try fallback
        return None

    # Fallback to fake database authentication
    user = get_user(username, db)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def verify_token(token: str) -> TokenData:
    """Verify and decode a JWT token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    return token_data


def get_current_user(token: str) -> User:
    """Get current user from token"""
    token_data = verify_token(token)
    user = get_user(username=token_data.username)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return User(username=user.username, is_admin=user.is_admin)
